##############################
# Builder Stage
##############################
FROM python:3.11-slim AS builder

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file to the working directory
COPY pyproject.toml requirements.lock ./

# Initial dependency installation
RUN uv sync --no-install-project --no-cache

# Install the project
COPY . .
RUN uv sync --no-cache

##############################
# Final Runtime Stage
##############################
FROM python:3.11-slim AS runtime

ENV PATH="/app/.venv/bin:$PATH"

WORKDIR /app

COPY --from=builder /app/ /app/

# Env
ARG api_key
ENV OPENAI_API_KEY="${api_key}"

ARG google_api_key
ENV GOOGLE_API_KEY="${google_api_key}"

ARG db_connection
ENV CONNECTION_STRING="${db_connection}"

ARG log_level
ENV LOG_LEVEL="${log_level}"

ARG log_format
ENV LOG_FORMAT_STDOUT="${log_format}"

ARG log_format
ENV LOG_FORMAT="${log_format}"

ARG ci_commit_short_sha
ENV CI_COMMIT_SHORT_SHA="${ci_commit_short_sha}"

ARG ci_commit_branch
ENV CI_COMMIT_BRANCH="${ci_commit_branch}"

ARG environment_tag
ENV ENVIRONMENT_TAG="${environment_tag}"

ARG built_at
ENV BUILT_AT="${built_at}"

# Sanity check
RUN if [ -z "$OPENAI_API_KEY" ]; then \
      echo 'Environment variable OPENAI_API_KEY must be specified. Exiting.'; exit 1; \
    fi && \
    if [ -z "$GOOGLE_API_KEY" ]; then \
      echo 'Environment variable GOOGLE_API_KEY must be specified. Exiting.'; exit 1; \
    fi && \
    if [ -z "$CONNECTION_STRING" ]; then \
      echo 'Environment variable CONNECTION_STRING must be specified. Exiting.'; exit 1; \
    fi && \
    if [ -z "$LOG_LEVEL" ]; then \
      echo 'Environment variable LOG_LEVEL must be specified. Exiting.'; exit 1; \
    fi && \
    if [ -z "$LOG_FORMAT" ]; then \
      echo 'Environment variable LOG_FORMAT must be specified. Exiting.'; exit 1; \
    fi && \
    if [ -z "$ENVIRONMENT_TAG" ]; then \
      echo 'Environment variable ENVIRONMENT_TAG must be specified. Exiting.'; exit 1; \
    fi

# Expose the port on which the application will run
EXPOSE 80

# Run the FastAPI application using uvicorn server
CMD ["uvicorn", "sportega_rockets.main:app", "--host", "0.0.0.0", "--port", "80"]
