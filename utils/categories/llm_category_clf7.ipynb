#%% md
# other + list of categories - spatne predikce jednokategorickeho clf - gemini
#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

#%%
df = pd.read_csv('categories_from_web_manual_flattened.csv',sep=';')
df
#%%
from pydantic import BaseModel
from typing import Literal, List

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist() + ['other']

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: List[Literal[tuple(categories)]]
#%%
API_KEY="AIzaSyDjEYajdEO5sK29D5rMTSR-JoIDPIMpOJ8"
from langchain.chat_models import init_chat_model
# llm = ChatOpenAI(
#             api_key=API_KEY,
#             temperature=0,
#             model="gemini-2.0-flash",
#             streaming=False,
#         )
llm = init_chat_model(model="gemini-2.0-flash-lite", model_provider="google_genai", temperature=0)
structured_llm = llm.with_structured_output(schema=Output, method="json_schema", strict=True)

prompt = f"""\
Decide which categories from the following categories the text belongs to. \
If the text belongs to none of the categories, return 'other'. \
If the can belong to multiple categories, return list of all possible categories.
The categories are: {categories}
The text is: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
question="jaka je nejdrazsi tenisova raketa?"
chain.invoke({"text": question}).category
#%%
question="jaka je dnes pocasi?"
chain.invoke({"text": question}).category
#%%
question="kolik máte modelů?"
chain.invoke({"text": question}).category
#%%
question="Jsou tyto pásky vodě odolné nebo se mohou poškodit při hře v dešti?"
chain.invoke({"text": question}).category
#%%
chain.invoke({"text": "Má tato páska reflexní prvky pro lepší viditelnost při hře za šera?"}).category
#%%
chain.invoke({"text": "Jaká láhev na nápoje je nejlehčí pro běh?"}).category
#%%
chain.invoke({"text": "prodas mi auto?"}).category
#%%
df_q = pd.read_csv('questions_with_categories_errors_anotated.csv')
df_q
#%%
df_q = df_q[df_q.error=='predicted'].copy()
#%%
df_q['predicted_list'] = df_q['user_input'].apply(lambda x: chain.invoke({"text": x}).category)
#%% md
stejna chyba i pro function_calling
#%% md
dal je zbytek jineho file
#%% md
pro gemini flash to padalo na nejakych limitechRetrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
].
#%%
pd.set_option('display.max_colwidth', None)
pd.set_option('display.max_rows', None)
#%%
df_q[['user_input','category','category_predicted','predicted_list']]
#%%
df_q['pokryto_seznamem'] = df_q.apply(lambda x: x['category'] in x['predicted_list'], axis=1)
#%%
df_q['pokryto_seznamem'].value_counts()
#%% md
tedy seznam vyresi 2/3 chybnych klasifikaci
#%% md
pokud budu predpokladat, ze co bylo spravne u klasifikace s jednim vysledkem by bylo i se seznamem mam 37 z 5000 pripadu spatne = 7 promile.. pro ted to staci