# Sportega Rockets AI Core

## Requires

- Rye https://rye.astral.sh/guide/installation/

## Quickstart

Run

```sh
<NAME_EMAIL>:robosales.ai/ai-core/sportega-rockets.git
cd sportega-rockets

# Prepare .env file in this directory
cp .env.template .env
$EDITOR .env

rye sync
source .venv/bin/activate
fastapi dev src/sportega_rockets/main.py --port 8080
```

Swagger will be runnning on http://localhost:8080/docs.
API will be running on http://localhost:8080/api/chat/answer/

## Testing with client

```
rye sync
source .venv/bin/activate
python3 tests/test_client.py
```


## More information

### Rye VS Code behavior

- VS Code supports the .venv directory created by `rye`. Use `Python: Select Interpreter` and select an environment in `.venv` directory

### Rye installation process

- https://rye.astral.sh/guide/installation/

### Docker compose start

```sh
<NAME_EMAIL>:robosales.ai/ai-core/sportega-rockets.git
cd sportega-rockets
docker-compose up --build
# Port redirects to 8080
```

### Why `source .venv/bin/activate`

That ensures you are running the app in the correct environment. Alternatively you can do `rye run <command>`
