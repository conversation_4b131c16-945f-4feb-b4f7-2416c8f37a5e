#%% md
### Imports
#%%
import numpy as np
import pandas as pd
import dotenv
import os
import re
import bm25s
import Stemmer
import xml.etree.ElementTree as ET

from pgvector.sqlalchemy import Vector
from sqlalchemy import create_engine, text, TEXT, types
from langchain_openai import OpenAIEmbeddings
from langchain.chat_models import init_chat_model
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate

dotenv.load_dotenv()
OPENAI_KEY = os.environ["OPENAI_API_KEY"]
#%% md
### Constants
#%%
XML_FILE = 'sportobchod-cz-google-nakupy-cz-2-158506a20c1215dd7081a772d2b1eb92.xml'
CATEGORY = 'Sportega > Tenis > Výplety'
TABLE_NAME = 'tennis_strings_products'
TABLE_NAME_DESCRIPTIONS = 'tennis_strings_descriptions'
TABLE_NAME_EMBEDDINGS = "tennis_strings_embeddings_binary"
BM25_INDEX_NAME = 'tennis string'
CONNECTION_STRING = "postgresql://dev:<EMAIL>/sportega"
LLM_FOR_DESCRIPTION_ALTERING = init_chat_model(model="gemini-2.0-flash", model_provider="google_genai", temperature=0)

#%% md
### Show all categories
#%%
# Načtení XML souboru
tree = ET.parse(XML_FILE)
root = tree.getroot()

# Inicializace seznamu pro uložení dat
data = []

# Iterace přes všechny položky <item>
for item in root.findall('.//item'):
    item_data = {}
    params = []
    for child in item:
        if child.tag == 'PARAM':
            param_name = child.find('PARAM_NAME').text
            param_value = child.find('VAL').text if child.find('VAL') is not None else None
            if param_name in params:
                print(f'id {item_data["id"]}')
                print(f'old {item_data[param_name]}')
                print(f'new {param_value}')
            item_data[param_name] = param_value
            params.append(param_name)
        else:
            item_data[child.tag] = child.text
    data.append(item_data)

# Vytvoření DataFrame z extrahovaných dat
df = pd.DataFrame(data)
pd.set_option('display.max_colwidth', None)

# Rename common columns only if they exist
columns_to_rename = {
    '{http://base.google.com/ns/1.0}image_link': 'image_link',
    '{http://base.google.com/ns/1.0}price': 'price_czk',
    '{http://base.google.com/ns/1.0}availability': 'availability',
    '{http://base.google.com/ns/1.0}brand': 'brand',
    '{http://base.google.com/ns/1.0}color': 'color',
    '{http://base.google.com/ns/1.0}material': 'material',
    '{http://base.google.com/ns/1.0}product_type': 'product_type',
    '{http://base.google.com/ns/1.0}item_group_id': 'item_group_id'
}

# Filter to only include columns that exist in the dataframe
existing_columns = {col: new_col for col, new_col in columns_to_rename.items() if col in df.columns}
df.rename(columns=existing_columns, inplace=True)

# Columns to drop
columns_to_drop = [
    '{http://base.google.com/ns/1.0}gtin',
    '{http://base.google.com/ns/1.0}identifier_exists',
    'stock',
    'rozmery_baleni',
    'rozmery_produktu',
    '{http://base.google.com/ns/1.0}custom_label_4',
    '{http://base.google.com/ns/1.0}custom_label_1',
    '{http://base.google.com/ns/1.0}custom_label_2',
    '{http://base.google.com/ns/1.0}custom_label_3',
    'delivery_date',
    'product_length',
    'product_width',
    'product_height',
    'shipping_length',
    'shipping_width',
    'shipping_height',
    'shipping_weight',
    '{http://base.google.com/ns/1.0}ads_grouping',
    '{http://base.google.com/ns/1.0}sale_price',
    '{http://base.google.com/ns/1.0}additional_image_link',
    'customs_number'
]

df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

# Save the csv
df.to_csv("catalog_all_products.csv", index=False)

df['product_type'].unique().tolist()

#%% md
### Save Raw category catalog
#%%
df = pd.read_csv('catalog_all_products.csv')
df = df[df.product_type.str.contains(CATEGORY, na=False, case=False)]
# Drop all none columns
df = df.dropna(axis=1, how='all')
# Drop all columns where there is only one unique value
df = df.loc[:, df.nunique() > 1]
df.to_csv(f'catalog_{CATEGORY}_raw.csv', index=False)

#%%
CUSTOM_COLUMNS_TO_DROP = ['{http://base.google.com/ns/1.0}size', ]
TITLE_PREFIXES_TO_DROP = ['Tenisový výplet', 'Tenisové výplety']
#%% md
### Data transformation
#%%
df = pd.read_csv(f'catalog_{CATEGORY}_raw.csv')

# create new columns if they don't exist
if 'item_group_id' not in df.columns and 'id' in df.columns:
    df['item_group_id'] = df['id']
if 'link_master' not in df.columns and 'link' in df.columns:
    df['link_master'] = df['link']
if 'image_link_master' not in df.columns and 'image_link' in df.columns:
    df['image_link_master'] = df['image_link']

# Fill missing values
df['link_master'] = df['link_master'].fillna(df['link'])
df['image_link_master'] = df['image_link_master'].fillna(df['image_link'])
df['item_group_id'] = df['item_group_id'].fillna(df['id'])

# Drop custom columns
df = df.drop(columns=[col for col in CUSTOM_COLUMNS_TO_DROP if col in df.columns])

# Manage the description column
df = df.drop(columns="description") if "description" in df.columns else df
# Rename the description column only if it exists
if "{http://base.google.com/ns/1.0}description" in df.columns:
    df = df.rename(columns={"{http://base.google.com/ns/1.0}description": "description"})

# convert id and item_group_id from int to string, but before turning them from float to str, make sure you omit the .0 after numbers
df[['id', 'item_group_id']] = df[['id', 'item_group_id']].applymap(
    lambda x: str(int(x)) if isinstance(x, float) else str(x)
)

# strip price of all that is non numerical and convert to float.
df['price_czk'] = df['price_czk'].str.replace(r'\D', '', regex=True).astype(int)

# Availability unification
df['availability'] = df['availability'].str.replace('in stock', 'in_stock', case=False)

# deal with age column
if any(col for col in df.columns if 'věk' in col.lower()):
    def extract_age_range(age_str):
        if pd.isna(age_str):
            return np.nan, np.nan
        match = re.match(r'(\d+)-(\d+)\s*let', str(age_str))
        if match:
            min_age = int(match.group(1))
            max_age = int(match.group(2))
            return min_age, max_age
        else:
            return np.nan, np.nan


    age_column = next(col for col in df.columns if 'věk' in col.lower())
    age_tuples = df[age_column].apply(extract_age_range)
    df['min_age'] = age_tuples.apply(lambda x: x[0] if isinstance(x, tuple) else np.nan)
    df['max_age'] = age_tuples.apply(lambda x: x[1] if isinstance(x, tuple) else np.nan)

# drop products with same title
df[df.duplicated(['title'], keep=False)]['link'].tolist()
df.drop_duplicates(subset=['title'], keep='first', inplace=True)

# if there are columns with string 'hmotnost' then drop the nonnumeric values
if any(col for col in df.columns if 'hmotnost' in col.lower()):
    for col in df.columns:
        if 'hmotnost' in col.lower():
            df[col] = df[col].str.replace(r'[^\d]', '', regex=True).astype(int)

# drop prefix from title
for title_prefix in TITLE_PREFIXES_TO_DROP:
    df['title'] = df['title'].str.replace(f'{title_prefix} ', '')

if 'Barva' in df.columns and 'color' in df.columns:
    df.drop(columns=['color'], inplace=True)

if 'Barva' in df.columns:
    df['Barva'] = df['Barva'].str.replace(',', '/')

if 'Materiál' in df.columns and 'material' in df.columns:
    df.drop(columns=['Materiál'], inplace=True)

# Clean up title
    if 'title' in df.columns:
        df['title'] = df['title'].str.replace(r'[\(\)]', '', regex=True)
        df['title'] = df['title'].str.replace(r'(\d),(\d)', r'\1.\2', regex=True)
        df['title'] = df['title'].str.replace(',', '', regex=True)
        df['title'] = df['title'].str.replace(r'\s+', ' ', regex=True)
        df['title'] = df['title'].str.strip()

        df['group_title'] = df['title']

df.to_csv(f'catalog_{CATEGORY}_processed.csv', index=False)



#%% md
### CUSTOM TRANSFORMATIONS
#%%
# Read the csv file
df = pd.read_csv(f'catalog_{CATEGORY}_processed.csv')


def clean_measurements(text):
    """Remove measurements in format X.XX mm from the text"""
    if pd.isna(text):
        return text
    # Remove patterns like '1.28 mm' or '1.28mm' with optional spaces
    return re.sub(r'\s*\d+[\.,]?\d*\s*mm\b', '', str(text))


df['group_title'] = df['group_title'].apply(clean_measurements)

# Fill all none values in "Velikost" column with values from "Průměr struny" column
df['Velikost'] = df['Velikost'].fillna(df['Průměr struny'])
# drop průměr struny column
df = df.drop(columns=['Průměr struny'])
# Rename columns
df = df.rename(columns={'Velikost': 'Průměr struny [mm]', 'Typ výpletu': 'Typ vlákna'})
# delete all mm values and spaces from column 'Průměr struny [mm]'
df['Průměr struny [mm]'] = df['Průměr struny [mm]'].str.replace(' mm', '')
df['Průměr struny [mm]'] = df['Průměr struny [mm]'].str.replace(',', '.')
df['Průměr struny X [mm]'] = df['Průměr struny [mm]'].str[:4]
df['Průměr struny Y [mm]'] = df['Průměr struny [mm]'].str[-4:]

# delete all m values and spaces from column 'Délka výpletu'
df['Délka výpletu'] = df['Délka výpletu'].str.replace(' m', '')
df['Délka výpletu'] = df['Délka výpletu'].str.replace(',', '.')

df = df.rename(columns={'Délka výpletu': 'Délka výpletu [m]'})

# rename Maximální napětí struny column to 'Maximální napětí struny [kg]'
df = df.rename(columns={'Maximální napětí struny': 'Maximální napětí struny [kg]'})
# strip the content of the kg and all spaces
df['Maximální napětí struny [kg]'] = df['Maximální napětí struny [kg]'].str.replace('kg', '').str.replace(' ', '')
df['Maximální napětí struny X [kg]'] = df['Maximální napětí struny [kg]'].str[:4]
df['Maximální napětí struny Y [kg]'] = df['Maximální napětí struny [kg]'].str[-4:]

# create new columns image_link_master and link_master
df['image_link_master'] = df['image_link']
df['link_master'] = df['link']

#rename column Určeno pro kategorii hráčů to player_level
df = df.rename(columns={'Určeno pro kategorii hráčů': 'player_level'})
df = df.rename(columns={'Typ výletu': 'Typ vlákna výpletu'})
df = df.rename(columns={'Balení': 'Typ balení'})
# delete all strings that says mírně a středně 
df['player_level'] = df['player_level'].str.replace('mírně', '').str.replace('středně', '')
df['player_level'] = df['player_level'].str.replace(' a ', ' ')

# drop column hodnocení komfortu a počet vrstev
df = df.drop(columns=['Hodnocení komfortu', 'Počet vrstev'])

# Extract text after last '>' in product_type ppend to 'Zaměření výpletu' with a comma separator
df['product_type_suffix'] = df['product_type'].str.extract(r'([^>]*)$', expand=False).str.strip()
df['Zaměření výpletu'] = df.apply(
    lambda x: f"{x['Zaměření výpletu']}, {x['product_type_suffix']}"
    if pd.notna(x['product_type_suffix']) and x['product_type_suffix'] != ''
    else x['Zaměření výpletu'],
    axis=1
)
df = df.drop(columns=['product_type', 'product_type_suffix'])

df['group_title'] = df['group_title'].str.replace(r'\b\d\.\d+\b', '', regex=True)


# save the df to csv file
df.to_csv(f'catalog_{CATEGORY}_processed.csv', index=False)


#%% md
### Final check
#%%
# show rows with missing critical data
print(df[df['id'].isnull() | df['item_group_id'].isnull() | df['image_link'].isnull() | df['link'].isnull() | df[
    'title'].isnull() | df['group_title'].isnull() | df['rank'].isnull() | df['price_czk'].isnull() | df[
             'image_link_master'].isnull() | df['link_master'].isnull()])

# Check duplicates in specific columns
print("ID duplicates:")
print(df[df.duplicated(['id'], keep=False)])

print("\nTitle duplicates:")
print(df[df.duplicated(['title'], keep=False)])

print("\nLink duplicates:")
print(df[df.duplicated(['link'], keep=False)])


#%% md
### Load csv for further db actions.
#%%
# load csv
df = pd.read_csv(f'catalog_{CATEGORY}_processed.csv')
# change types
df['Délka výpletu [m]'] = df['Délka výpletu [m]'].astype(float)
df['Průměr struny X [mm]'] = df['Průměr struny X [mm]'].astype(float)
df['Průměr struny Y [mm]'] = df['Průměr struny Y [mm]'].astype(float)
df['Maximální napětí struny X [kg]'] = df['Maximální napětí struny X [kg]'].astype(float)
df['Maximální napětí struny Y [kg]'] = df['Maximální napětí struny Y [kg]'].astype(float)
df['id'] = df['id'].astype(str)
df['item_group_id'] = df['item_group_id'].astype(str)

df.dtypes
#%% md
### Save tabular data to database
#%%

# --- Create SQLAlchemy Engine ---
engine = create_engine(CONNECTION_STRING)

# --- Use df.to_sql with dtype mapping ---
try:
    df.to_sql(
        name=TABLE_NAME,
        con=engine,
        if_exists='replace',
        index=False,
    )
    print(f"DataFrame successfully saved to table '{TABLE_NAME}'.")
except Exception as e:
    print(f"Error saving DataFrame to SQL: {e}")

engine.dispose()
#%% md
### Create a description table in database
#%%
llm = LLM_FOR_DESCRIPTION_ALTERING
# Be concise and accurate.
# Create a prompt template with placeholders
prompt_template = PromptTemplate.from_template(
    """
    Change the following product description, but exclude information that is already present in the data table.
    If the description mentions anything from the data table. Remove it from the description.
    Do not add any additional information.
    Be accurate. Keep the answer short, but do not leave out any important information beside the data table.
    Description: {description}\n
    Data table to be excluded from description: {other_fields}\n
    Return the description in English language.
    """
)


def generate_altered_description(row):
    description = row['description']
    if not description or pd.isna(description):
        return ""

    other_fields = {col: row[col] for col in row.index if
                    col not in ['description', 'rank', 'id', 'link', 'image_link', 'item_group_id', 'link_master',
                                'image_link_master', 'group_title']}
    other_fields_str = "; ".join([f"{k}: {v}" for k, v in other_fields.items() if pd.notna(v) and v != ""])
    try:
        prompt = prompt_template.format(description=description, other_fields=other_fields_str)
        response = llm.invoke(prompt)
        return response.content.strip()
    except Exception as e:
        print(f"Error generating description: {e}")
        return description


# Apply the function to each row
df['description_cleaned'] = df.apply(generate_altered_description, axis=1)

# Display both original and altered descriptions for comparison
for i in range(5):
    print(f"Record {i}:")
    print(f"Original: {df.loc[i, 'description']}")
    print(f"Altered: {df.loc[i, 'description_cleaned']}")
    print("-" * 50)

embedding_dim = 3072
embeddings_model = OpenAIEmbeddings(model="text-embedding-3-large", dimensions=embedding_dim,
                                    api_key=os.environ["OPENAI_API_KEY"])
descriptions = df['description_cleaned'].tolist()
embeddings_list = embeddings_model.embed_documents(descriptions)
df['description_cleaned_embed'] = embeddings_list

#save df
df_description = df[['id', 'item_group_id', 'description', 'description_cleaned', 'description_cleaned_embed']]
df_description.to_csv(f'{CATEGORY}_description_with_embeddings.csv', index=False)

# --- Create SQLAlchemy Engine ---
engine = create_engine(CONNECTION_STRING)

# ---Enable pgvector extension if not already done ---
try:
    with engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
        conn.commit()
    print("pgvector extension checked/created.")
except Exception as e:
    print(f"Could not check/create pgvector extension: {e}")

# --- Use df.to_sql with dtype mapping ---
try:
    df_description.to_sql(
        name=TABLE_NAME_DESCRIPTIONS,
        con=engine,
        if_exists='replace',
        index=False,
        dtype={'description_cleaned_embed': Vector(embedding_dim),
               'id': TEXT,
               'item_group_id': TEXT,
               }
    )
    print(f"DataFrame successfully saved to table '{TABLE_NAME_DESCRIPTIONS}'.")
except Exception as e:
    print(f"Error saving DataFrame to SQL: {e}")


#%% md
### Get the BM25 index
#%%
# Create the BM25 index
#  ---  Please read: https://github.com/xhluca/bm25s ---

# Step 1: Load the DataFrame
engine = create_engine(CONNECTION_STRING)
df_description = pd.read_sql_table(TABLE_NAME_DESCRIPTIONS, engine)

corpus = [doc.description_cleaned for doc in df_description.itertuples()]
stemmer = Stemmer.Stemmer('english')
corpus_tokens = bm25s.tokenize(corpus, stopwords='en', stemmer=stemmer)

retriever = bm25s.BM25()
retriever.index(corpus_tokens)

# Save the BM25 index to disk
retriever.save(f'/home/<USER>/projects/sportega-rockets/data/bm25_index/{BM25_INDEX_NAME}', corpus=corpus)
#%% md
### Create products embeddings table in the database
#%%

# Define columns to process
cols_to_leave_out = ['description', 'rank', 'id', 'link', 'image_link', 'item_group_id', 'link_master',
                     'image_link_master']

cols_to_extract = [col for col in df.columns if
                   (col not in cols_to_leave_out) and (df[col].dtype != 'int64') and (df[col].dtype != 'float64')]


def get_unique_strings_from_db(connection_string: str, table_name: str, columns: list[str]) -> list[str]:
    """
    Connects to a database, reads specified columns from a table,
    and returns a list of unique non-null string values from those columns.
    """
    unique_strings = set()
    engine = None

    try:
        engine = create_engine(connection_string)

        chunk_size = 10000
        for chunk_df in pd.read_sql_table(table_name, engine, chunksize=chunk_size):
            valid_columns = [col for col in columns if col in chunk_df.columns]

            for col in valid_columns:

                try:
                    # Using dropna() is efficient
                    unique_values = chunk_df[col].dropna().unique()
                    # Add only string values to the set
                    unique_strings.update(val for val in unique_values if isinstance(val, str))
                except Exception as col_err:
                    print(f"Error processing column '{col}': {col_err}")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        if engine:
            engine.dispose()

    return sorted(list(unique_strings))  # Return sorted list for consistency


def extract_bytes(embedding: list[float]) -> bytes:
    """Converts a list of floats (embedding) into bytes."""
    return np.array(embedding, dtype=np.float64).tobytes()


try:

    all_unique_values = get_unique_strings_from_db(CONNECTION_STRING, TABLE_NAME, cols_to_extract)
    print(
        f"Found {len(all_unique_values)} unique string values from columns {cols_to_extract} in table '{TABLE_NAME}'.")
    print(f"Unique values:{all_unique_values}]")


except Exception as main_err:
    print(f"Failed to get unique values: {main_err}")

if len(all_unique_values) > 0:
    print(f"Found {len(all_unique_values)} unique values to embed.")

    # Ensure OPENAI_API_KEY is set
    if "OPENAI_API_KEY" not in os.environ:
        raise ValueError("OPENAI_API_KEY environment variable not set.")

    try:
        # 1. Initialize Embeddings Model
        embeddings_model = OpenAIEmbeddings(model="text-embedding-3-large", dimensions=3072,
                                            api_key=os.environ["OPENAI_API_KEY"])

        # 2. Generate Embeddings
        print("Generating embeddings...")
        embeddings_list = embeddings_model.embed_documents(all_unique_values)
        print(f"Generated {len(embeddings_list)} embeddings with dimension {len(embeddings_list[0])}.")
        print(f"Generated {len(embeddings_list)} embeddings.")

        # 3. Convert embeddings to bytes
        embeds_b = list(map(extract_bytes, embeddings_list))
        print("Converted embeddings to binary format.")

        # 4. Create DataFrame
        df_embeddings = pd.DataFrame({"word": all_unique_values, "binary": embeds_b})
        print("Created DataFrame:")
        print(df_embeddings.head())

        # 5. Save DataFrame to PostgreSQL
        print(f"Connecting to database: {CONNECTION_STRING}")
        engine = create_engine(CONNECTION_STRING)

        print(f"Saving DataFrame to table '{TABLE_NAME_EMBEDDINGS}'...")
        df_embeddings.to_sql(
            TABLE_NAME_EMBEDDINGS,
            engine,
            if_exists="replace",
            index=False,
            dtype={"binary": types.LargeBinary}
        )
        print(f"Successfully saved embeddings to table '{TABLE_NAME_EMBEDDINGS}")

    except Exception as e:
        print(f"An error occurred: {e}")

else:
    print("The list 'all_unique_values' is empty. No embeddings were generated or saved.")