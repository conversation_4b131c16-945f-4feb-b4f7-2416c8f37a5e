import requests
import json
import random
import string
import sys
import os
import argparse
from pprint import pprint
import time
import pandas as pd

url = "http://localhost:8080/api/chat/answer"

# Paths
DEFAULT_INPUT_FILE = "all_product_questions.csv"
input_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, f"component tests/{DEFAULT_INPUT_FILE}"))
output_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, "component tests/complex_rag_results.csv"))


def main():
    parser = argparse.ArgumentParser(description="Run complex RAG test with questions from a CSV file.")
    parser.add_argument(
        "--input_file",
        type=str,
        default=DEFAULT_INPUT_FILE,
        help=f"Input CSV file containing test cases (default: {DEFAULT_INPUT_FILE})",
    )
    args = parser.parse_args()

    # Update input_path based on the argument
    input_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, f"component tests/{args.input_file}"))

    # Load questions from CSV
    try:
        df = pd.read_csv(input_path)
    except FileNotFoundError:
        print(f"Error: Input file not found at {input_path}")
        sys.exit(1)

    if "user_input" not in df.columns:
        raise ValueError("Column 'user_input' not found in the input CSV file")

    results = []
    results_df = pd.DataFrame()

    for i, user_input in enumerate(df["user_input"]):
        flowid = "".join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
        data = {"query": f"{user_input}", "user_id": "user", "solver_id": "sportega-rackets", "flow_id": flowid, "language": "eng"}
        print(f"Iterace {i} flowid: {flowid}")
        print(f"USER: {user_input}")
        try:
            start = time.time()
            response = requests.post(url, json=data)
            end = time.time()
            response_time = end - start
        except Exception as e:
            print(f"Failed with error: {e}")
            result = {"user_input": user_input, "error": str(e), "response": None, "response_time": None}
            results.append(result)
            results_df = pd.concat([results_df, pd.DataFrame([result])], ignore_index=True)
            results_df.to_csv(output_path, index=False)
            continue

        if response.status_code == 200:
            response_json = json.loads(response.text)
            output_text = response_json.get("output_text", "")
            products = response_json.get("products", [])
            result = {"user_input": user_input, "output_text": output_text, "products": products, "response_time": response_time}
            results.append(result)
            print("Chatbot: ", output_text)
        else:
            print("Chyba, status kód:", response.status_code)
            result = {
                "user_input": user_input,
                "error": f"Status code: {response.status_code}",
                "response": None,
                "response_time": response_time,
            }
            results.append(result)

        # Append and save after every iteration
        results_df = pd.concat([results_df, pd.DataFrame([result])], ignore_index=True)
        results_df.to_csv(output_path, index=False)

    # Final print for completion
    print(f"Results saved to {output_path}")


if __name__ == "__main__":
    main()
