#%%
import json
import pandas as pd
df  = pd.read_csv('messages_export.csv')
df
#%%
df['answer_cz'] = df.message.apply(lambda x: json.loads(x)['answer'])
#%%
from langchain.llms import OpenAI

# Set up your OpenAI API key
llm = OpenAI(api_key='*******************************************************************************************************************************************')

def translate_to_english(text):
    prompt = f"Translate the following text to English: {text}"
    response = llm(prompt)
    return response.strip()
#%%
translate_to_english('Ahoj, jak se máš?')
#%%
df['answer_en'] = df['answer_cz'].apply(translate_to_english)
df
#%%
df.to_csv('messages_export_translated.csv', index=False)
#%%
df2 = pd.DataFrame({
    'answer': df['answer_cz'].tolist() + df['answer_en'].tolist(),
    'language': ['cz'] * len(df) + ['en'] * len(df)
})
df2
#%%
df2.to_csv('dataset_language.csv', index=False)
#%%
