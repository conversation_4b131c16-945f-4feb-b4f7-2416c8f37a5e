stages:
  - build 
  - deploy

variables:
  DOCKER_REGISTRY: ************.dkr.ecr.eu-central-1.amazonaws.com/private-sportega
  REPOSITORY_NAME: "private-sportega"

  AWS_ACCOUNT_ID: "************"
  AWS_ACCESS_KEY_ID: "********************"
  AWS_SECRET_ACCESS_KEY: "hrfbk25/DQXH+7VGEIn1F2HNAc/hnqgS0sWn9xeH"
  AWS_DEFAULT_REGION: "eu-central-1"

  DEV_ECS_CLUSTER: "development"
  STAGE_ECS_CLUSTER: "stage"
  PROD_ECS_CLUSTER: "prod-ecs-cluster"
  SERVICE_NAME: "sportega"
  dev_DB_CONNECTION: "postgresql://customer_sportega_app:<EMAIL>:5432/sportega"
  stage_DB_CONNECTION: "postgresql://customer_sportega_app:<EMAIL>:5432/sportega"
  main_DB_CONNECTION: ""
  dev_OPENAI_API_KEY: "*******************************************************************************************************************************************"
  stage_OPENAI_API_KEY: "**********************************************************************************************************************************************"
  dev_GOOGLE_API_KEY: "AIzaSyDjEYajdEO5sK29D5rMTSR-JoIDPIMpOJ8"
  stage_GOOGLE_API_KEY: "AIzaSyDjEYajdEO5sK29D5rMTSR-JoIDPIMpOJ8"
  dev_LOG_LEVEL: "DEBUG"
  stage_LOG_LEVEL: "DEBUG"
  dev_LOG_FORMAT: "JSON"
  stage_LOG_FORMAT: "JSON"


black:
  stage: build 
  image: python:3.10
  before_script:
    - pip install black
  script:
    - black --check . --line-length 139 --target-version py311
  allow_failure: false
  rules:
    - if: $CI_COMMIT_TAG == "dev" || $CI_COMMIT_TAG == "stage" || $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: always
    - when: never


build:
  stage: build
  image:
    name: richard1robosales1ai/docker-dind-awscli:latest
  services:
    - docker:dind
  script:
    - echo "Building and pushing image with commit tag $CI_COMMIT_SHA"
    -
      if [ "$CI_COMMIT_TAG" == "dev" ]; then
        TAG="dev";
        DB_CONNECTION=$dev_DB_CONNECTION;
        OPENAI_API_KEY=$dev_OPENAI_API_KEY;
        GOOGLE_API_KEY=$dev_GOOGLE_API_KEY;
        LOG_LEVEL=$dev_LOG_LEVEL;
        LOG_FORMAT=$dev_LOG_FORMAT;
      elif [ "$CI_COMMIT_TAG" == "stage" ]; then
        TAG="stage";
        DB_CONNECTION=$stage_DB_CONNECTION;
        OPENAI_API_KEY=$stage_OPENAI_API_KEY;
        GOOGLE_API_KEY=$stage_GOOGLE_API_KEY;
        LOG_LEVEL=$stage_LOG_LEVEL;
        LOG_FORMAT=$stage_LOG_FORMAT;
      else
        echo "Bad tag !";
        echo "Please only run this pipeline on dev or stage branches";
        exit 1;
      fi

    - echo "Building at $(date -u '+%Y-%m-%dT%H:%M:%SZ')"
    - 
      docker build
          --build-arg api_key="$OPENAI_API_KEY"
          --build-arg google_api_key="$GOOGLE_API_KEY"
          --build-arg db_connection="$DB_CONNECTION"
          --build-arg log_level="$LOG_LEVEL"
          --build-arg log_format="$LOG_FORMAT"
          --build-arg ci_commit_short_sha="$CI_COMMIT_SHORT_SHA"
          --build-arg ci_commit_branch="$CI_COMMIT_BRANCH"
          --build-arg environment_tag="$CI_COMMIT_TAG"
          --build-arg built_at="$(date -u '+%Y-%m-%dT%H:%M:%SZ')"
          -t "$DOCKER_REGISTRY:$CI_COMMIT_SHA" .
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY:$CI_COMMIT_SHA

  rules:
    - if: $CI_COMMIT_TAG == "dev" || $CI_COMMIT_TAG == "stage"
      when: always
    - when: never

deploy:
  stage: deploy
  image:
    name: richard1robosales1ai/docker-dind-awscli:latest

  services:
    - docker:dind

  script:
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker buildx imagetools create $DOCKER_REGISTRY:$CI_COMMIT_SHA --tag $DOCKER_REGISTRY:$CI_COMMIT_TAG

    - if [ "$CI_COMMIT_TAG" == "dev" ]; then CLUSTER="$DEV_ECS_CLUSTER"; fi
    - if [ "$CI_COMMIT_TAG" == "stage" ]; then CLUSTER="$STAGE_ECS_CLUSTER"; fi
    - echo "Using cluster $CLUSTER"
    - aws ecs update-service --cluster $CLUSTER --service $SERVICE_NAME-$CI_COMMIT_TAG --force-new-deployment
    - echo "Waiting for the new deployment to be stable..."
    - aws ecs wait services-stable --cluster $CLUSTER --services $SERVICE_NAME-$CI_COMMIT_TAG

  environment:
      name: $CI_COMMIT_TAG
  rules:
    - if: $CI_COMMIT_TAG == "dev" || $CI_COMMIT_TAG == "stage" || $CI_COMMIT_TAG == "prod"
      when: always
    - when: never


# todo this is literally just pasted not tested at all
#copy_postgres_dev_to_stage:
#  stage: deploy
#  image: postgres:latest
#  script:
#    - |
#      DUMP_FILE="/tmp/dump.sql"
#
#      echo "Starting the process with the following parameters:"
#      echo "Source connection: $SOURCE_CONN"
#      echo "Target connection: $TARGET_CONN"
#      echo "Tables: $TABLE_NAME"
#
#      for TABLE in $TABLE_NAME; do
#        echo "Processing table: $TABLE"
#
#        echo "Dumping the source table $TABLE..."
#        pg_dump "$SOURCE_CONN" --table="$TABLE" --data-only > "$DUMP_FILE"
#        echo "Dump completed for table $TABLE."
#
#        echo "Checking if the table $TABLE exists in the target database..."
#        TABLE_EXISTS=$(psql -Xq "$TARGET_CONN" -t -c "SELECT EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = '$TABLE');")
#
#        TABLE_EXISTS=$(echo "$TABLE_EXISTS" | xargs)
#
#        if [ "$TABLE_EXISTS" = "f" ]; then
#          echo "Table $TABLE does not exist. Creating the table..."
#          pg_dump "$SOURCE_CONN" --table="$TABLE" --schema-only > "$DUMP_FILE"
#          psql -1 -X "$TARGET_CONN" -f "$DUMP_FILE"
#          echo "Table $TABLE created successfully."
#        else
#          echo "Table $TABLE already exists in the target database."
#          echo "Truncating the target table $TABLE..."
#          psql -1 -X "$TARGET_CONN" -c "TRUNCATE TABLE $TABLE;"
#        fi
#
#        echo "Restoring data to the target table $TABLE..."
#        psql -1 -X "$TARGET_CONN" -f "$DUMP_FILE"
#        echo "Data restoration completed successfully for table $TABLE."
#
#        echo "Cleaning up the temporary dump file for $TABLE..."
#        rm "$DUMP_FILE"
#        echo "Temporary dump file cleaned up for table $TABLE."
#      done
#
#      echo "All tables processed successfully."
#  variables:
#    SOURCE_CONN: $DEV_DB_CONNECTION
#    TARGET_CONN: $STAGE_DB_CONNECTION
#    TABLE_NAME: products products_embeddings_binary
#  rules:
#    - if: $CI_COMMIT_TAG == "dev" || $CI_COMMIT_TAG == "stage"
#      when: manual 
#    - when: never
