import requests
import json
import random
import string

import sys
import os
from pprint import pprint
import time

url = "http://localhost:8080/api/chat/answer"

# user_inputs = [
#     "chtel bych raketu head s velikosti hlavy 800",
#     "chci si koupit tu druhou",
#     "jakou ma velikost gripu?",
#     "jaky z techto gripu mam vybrat pro manzelku",
#     "ok tak ja si vezmu L2",
# ]

user_inputs = [
    "dobrý den, jakou tenisovou raketu mi doporučíš?",
    "začátečník, výška 180 cm",
    "stačí si tu raketu koupit a můžu hrát nebo potřebuji ještě něco s raketou udělat? a nebo si musím koupit něco dalšího?",
]

# user_inputs = ["chci raketu pro moje dítě", "14", "memu dalsimu synovi je 13 taky potrebuje raketu"]

user_inputs = ["ukaz 10 raket"]

# user_inputs = [
#     "kolik raket znacky head mas v katalogu",
#     "kolik z nichje drazsi nez 4000 korun",
#     "ja si nejakou takovou raketu koupim",
# ]


for i in range(10):
    flowid = "".join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    print(f"Iterace {i} flowid: {flowid}")
    for user_input in user_inputs:
        data = {"query": f"{user_input}", "user_id": "user", "solver_id": "sportega-rackets", "flow_id": flowid, "language": "cze"}  # eng
        print(f"USER: {user_input}")
        try:
            start = time.time()
            response = requests.post(url, json=data)
            end = time.time()
            print(f"Response time: {end - start}")
        except Exception as e:
            print(f"Failed with error: {e}")
            continue

        if response.status_code == 200:

            response = json.loads(response.text)
            print("Chatbot: ", response["output_text"])

            for r in response["products"]:
                pprint(r)

        else:
            print("Chyba, status kód:", response.status_code)
