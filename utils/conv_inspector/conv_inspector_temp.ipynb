#%%
from dotenv import load_dotenv
import os

load_dotenv(override=True)

CONNECTION_STRING = os.environ.get("CONNECTION_STRING")
#%%
import json

def show_turns():
    df_turns_b = df[(df.name=='sportega_rockets.generate_response') & (df.line=='143')]
    df_turns_e = df[(df.name=='sportega_rockets.generate_response') & (df.line=='192')]
    df_turns = df_turns_b.merge(df_turns_e, on='turn_id', suffixes=('_b', '_e'))
    df_turns['input'] = df_turns['message_b'].apply(lambda x: json.loads(x)['query'])
    df_turns['answer'] = df_turns['message_e'].apply(lambda x: json.loads(x)['answer'])    

    df_turns = df_turns.merge(df.groupby('turn_id')['domain'].apply(lambda x: list(x.dropna().unique())), on='turn_id')
    return df_turns[['turn_id', 'input','answer','domain']]
#%%
import pandas as pd
import psycopg2

df=None

def read_conv(flowid:str):
    
    conn = psycopg2.connect(CONNECTION_STRING)

    query = f"""
    SELECT extra->>'domain' as domain, extra->>'turn_idx' as turn_id,  name, function, line, message, level, time
    FROM public.log
    WHERE extra->>'flowid' LIKE '%{flowid}%'
    ORDER BY time
    """

    global df
    df = pd.read_sql_query(query, conn)
    conn.close()
    return show_turns()
    

#%%
def show_turn(turn_id:int, domain:str = None, show_debug:bool = False):
    if domain:
        if show_debug:
            df_turn = df[(df.turn_id==str(turn_id)) & (df.domain==domain)]
        else:
            df_turn = df[(df.level=='INFO')&(df.turn_id==str(turn_id)) & (df.domain==domain)]
    else:
        if show_debug:
            df_turn = df[(df.turn_id==str(turn_id))]
        else:
            df_turn = df[(df.level=='INFO')&(df.turn_id==str(turn_id))]
    
    
    return df_turn[['name', 'function','domain','message']]
#%%
pd.set_option('display.max_colwidth', None)
read_conv("vLB19YnJPI")
#%%
show_turn(turn_id=3,domain='tenis > míčky')
#%%
