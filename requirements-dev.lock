# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.4
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
ansicolors==1.1.8
    # via papermill
anyio==4.9.0
    # via httpx
    # via jupyter-server
    # via openai
    # via starlette
argon2-cffi==23.1.0
    # via jupyter-server
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
arrow==1.3.0
    # via isoduration
asttokens==3.0.0
    # via stack-data
async-lru==2.0.5
    # via jupyterlab
attrs==25.3.0
    # via aiohttp
    # via jsonschema
    # via referencing
babel==2.17.0
    # via jupyterlab-server
beautifulsoup4==4.13.4
    # via googledriver
    # via nbconvert
black==24.8.0
    # via sportega-rockets
bleach==6.2.0
    # via nbconvert
bm25s==0.2.13
    # via sportega-rockets
cachetools==5.5.2
    # via google-auth
    # via sportega-rockets
certifi==2025.4.26
    # via httpcore
    # via httpx
    # via requests
cffi==1.17.1
    # via argon2-cffi-bindings
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via black
    # via papermill
    # via uvicorn
comm==0.2.2
    # via ipykernel
contourpy==1.3.2
    # via matplotlib
cycler==0.12.1
    # via matplotlib
dataclasses-json==0.6.7
    # via langchain-community
debugpy==1.8.14
    # via ipykernel
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via nbconvert
    # via odfpy
diskcache==5.6.3
    # via sportega-rockets
distro==1.9.0
    # via openai
entrypoints==0.4
    # via papermill
et-xmlfile==2.0.0
    # via openpyxl
executing==2.2.0
    # via stack-data
fastapi==0.115.12
    # via sportega-rockets
fastjsonschema==2.21.1
    # via nbformat
filelock==3.18.0
    # via googledriver
filetype==1.2.0
    # via langchain-google-genai
fonttools==4.58.1
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
frozenlist==1.6.0
    # via aiohttp
    # via aiosignal
google-ai-generativelanguage==0.6.18
    # via langchain-google-genai
google-api-core==2.24.2
    # via google-ai-generativelanguage
    # via google-api-python-client
google-api-python-client==2.170.0
    # via gsheet-pandas
google-auth==2.40.2
    # via google-ai-generativelanguage
    # via google-api-core
    # via google-api-python-client
    # via google-auth-httplib2
    # via google-auth-oauthlib
google-auth-httplib2==0.2.0
    # via google-api-python-client
    # via gsheet-pandas
google-auth-oauthlib==1.2.2
    # via gsheet-pandas
googleapis-common-protos==1.70.0
    # via google-api-core
    # via grpcio-status
googledriver==0.1.7
graphviz==0.20.3
greenlet==3.2.2
    # via sqlalchemy
grpcio==1.71.0
    # via google-api-core
    # via grpcio-status
grpcio-status==1.71.0
    # via google-api-core
gsheet-pandas==0.2.6
h11==0.16.0
    # via httpcore
    # via uvicorn
hatchling==1.27.0
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via google-api-python-client
    # via google-auth-httplib2
httpx==0.28.1
    # via jupyterlab
    # via langgraph-sdk
    # via langsmith
    # via openai
httpx-sse==0.4.0
    # via langchain-community
idna==3.10
    # via anyio
    # via httpx
    # via jsonschema
    # via requests
    # via yarl
ipykernel==6.29.5
    # via jupyterlab
ipython==9.2.0
    # via ipykernel
ipython-pygments-lexers==1.1.1
    # via ipython
isoduration==20.11.0
    # via jsonschema
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via jupyter-server
    # via jupyterlab
    # via jupyterlab-server
    # via nbconvert
jiter==0.10.0
    # via openai
joblib==1.5.1
    # via scikit-learn
json5==0.12.0
    # via jupyterlab-server
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
    # via jsonschema
jsonschema==4.24.0
    # via jupyter-events
    # via jupyterlab-server
    # via nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter-client==8.6.3
    # via ipykernel
    # via jupyter-server
    # via nbclient
jupyter-core==5.8.1
    # via ipykernel
    # via jupyter-client
    # via jupyter-server
    # via jupyterlab
    # via nbclient
    # via nbconvert
    # via nbformat
jupyter-events==0.12.0
    # via jupyter-server
jupyter-lsp==2.2.5
    # via jupyterlab
jupyter-server==2.16.0
    # via jupyter-lsp
    # via jupyterlab
    # via jupyterlab-server
    # via notebook-shim
jupyter-server-terminals==0.5.3
    # via jupyter-server
jupyterlab==4.4.3
jupyterlab-pygments==0.3.0
    # via nbconvert
jupyterlab-server==2.27.3
    # via jupyterlab
kiwisolver==1.4.8
    # via matplotlib
langchain==0.3.25
    # via langchain-community
    # via sportega-rockets
langchain-community==0.3.24
    # via langchain-experimental
    # via sportega-rockets
langchain-core==0.3.62
    # via langchain
    # via langchain-community
    # via langchain-experimental
    # via langchain-google-genai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via langgraph-prebuilt
    # via sportega-rockets
langchain-experimental==0.3.4
    # via sportega-rockets
langchain-google-genai==2.1.5
    # via sportega-rockets
langchain-openai==0.3.18
    # via sportega-rockets
langchain-text-splitters==0.3.8
    # via langchain
    # via sportega-rockets
langgraph==0.4.7
    # via sportega-rockets
langgraph-checkpoint==2.0.26
    # via langgraph
    # via langgraph-prebuilt
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.43
    # via langchain
    # via langchain-community
    # via langchain-core
loguru==0.7.2
    # via sportega-rockets
markupsafe==3.0.2
    # via jinja2
    # via nbconvert
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.3
    # via sportega-rockets
matplotlib-inline==0.1.7
    # via ipykernel
    # via ipython
mistune==3.1.3
    # via nbconvert
multidict==6.4.4
    # via aiohttp
    # via yarl
mypy-extensions==1.1.0
    # via black
    # via typing-inspect
nbclient==0.10.2
    # via nbconvert
    # via papermill
nbconvert==7.16.6
    # via jupyter-server
nbformat==5.10.4
    # via jupyter-server
    # via nbclient
    # via nbconvert
    # via papermill
nest-asyncio==1.6.0
    # via ipykernel
notebook-shim==0.2.4
    # via jupyterlab
numpy==1.26.4
    # via bm25s
    # via contourpy
    # via langchain-community
    # via matplotlib
    # via pandas
    # via pgvector
    # via scikit-learn
    # via scipy
    # via sportega-rockets
oauthlib==3.2.2
    # via requests-oauthlib
odfpy==1.4.1
openai==1.82.0
    # via langchain-openai
    # via sportega-rockets
openpyxl==3.1.5
    # via sportega-rockets
orjson==3.10.18
    # via langgraph-sdk
    # via langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
overrides==7.7.0
    # via jupyter-server
packaging==24.2
    # via black
    # via hatchling
    # via ipykernel
    # via jupyter-events
    # via jupyter-server
    # via jupyterlab
    # via jupyterlab-server
    # via langchain-core
    # via langsmith
    # via marshmallow
    # via matplotlib
    # via nbconvert
pandas==2.2.2
    # via gsheet-pandas
    # via sportega-rockets
pandocfilters==1.5.1
    # via nbconvert
papermill==2.6.0
    # via sportega-rockets
parso==0.8.4
    # via jedi
pathspec==0.12.1
    # via black
    # via hatchling
pexpect==4.9.0
    # via ipython
pgvector==0.4.1
    # via sportega-rockets
pillow==11.2.1
    # via matplotlib
platformdirs==4.3.8
    # via black
    # via jupyter-core
pluggy==1.6.0
    # via hatchling
prometheus-client==0.22.0
    # via jupyter-server
prompt-toolkit==3.0.51
    # via ipython
propcache==0.3.1
    # via aiohttp
    # via yarl
proto-plus==1.26.1
    # via google-ai-generativelanguage
    # via google-api-core
protobuf==5.29.5
    # via google-ai-generativelanguage
    # via google-api-core
    # via googleapis-common-protos
    # via grpcio-status
    # via proto-plus
psutil==7.0.0
    # via ipykernel
psycopg2-binary==2.9.10
    # via sportega-rockets
ptyprocess==0.7.0
    # via pexpect
    # via terminado
pure-eval==0.2.3
    # via stack-data
py-spy==0.4.0
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.5
    # via fastapi
    # via langchain
    # via langchain-core
    # via langchain-google-genai
    # via langgraph
    # via langsmith
    # via openai
    # via pydantic-settings
    # via sportega-rockets
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via langchain-community
pygments==2.19.1
    # via ipython
    # via ipython-pygments-lexers
    # via nbconvert
pyparsing==3.2.3
    # via httplib2
    # via matplotlib
pysocks==1.7.1
    # via requests
pystemmer==3.0.0
    # via sportega-rockets
python-dateutil==2.9.0.post0
    # via arrow
    # via jupyter-client
    # via matplotlib
    # via pandas
python-dotenv==1.0.1
    # via gsheet-pandas
    # via pydantic-settings
    # via sportega-rockets
python-json-logger==3.3.0
    # via jupyter-events
pytz==2024.1
    # via pandas
    # via sportega-rockets
pyyaml==6.0.2
    # via jupyter-events
    # via langchain
    # via langchain-community
    # via langchain-core
    # via papermill
    # via sportega-rockets
pyzmq==26.4.0
    # via ipykernel
    # via jupyter-client
    # via jupyter-server
referencing==0.36.2
    # via jsonschema
    # via jsonschema-specifications
    # via jupyter-events
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via google-api-core
    # via googledriver
    # via jupyterlab-server
    # via langchain
    # via langchain-community
    # via langsmith
    # via papermill
    # via requests-oauthlib
    # via requests-toolbelt
    # via tiktoken
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
requests-toolbelt==1.0.0
    # via langsmith
rfc3339-validator==0.1.4
    # via jsonschema
    # via jupyter-events
rfc3986-validator==0.1.1
    # via jsonschema
    # via jupyter-events
rpds-py==0.25.1
    # via jsonschema
    # via referencing
rsa==4.9.1
    # via google-auth
scikit-learn==1.5.1
    # via sportega-rockets
scipy==1.13.0
    # via bm25s
    # via scikit-learn
    # via sportega-rockets
send2trash==1.8.3
    # via jupyter-server
setuptools==80.9.0
    # via jupyterlab
six==1.17.0
    # via googledriver
    # via python-dateutil
    # via rfc3339-validator
sniffio==1.3.1
    # via anyio
    # via openai
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.30
    # via langchain
    # via langchain-community
    # via sportega-rockets
stack-data==0.6.3
    # via ipython
starlette==0.46.2
    # via fastapi
tabulate==0.9.0
    # via sportega-rockets
tenacity==9.1.2
    # via langchain-community
    # via langchain-core
    # via papermill
terminado==0.18.1
    # via jupyter-server
    # via jupyter-server-terminals
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.7.0
    # via langchain-openai
    # via sportega-rockets
tinycss2==1.4.0
    # via bleach
tornado==6.5.1
    # via ipykernel
    # via jupyter-client
    # via jupyter-server
    # via jupyterlab
    # via terminado
tqdm==4.67.1
    # via googledriver
    # via openai
    # via papermill
traitlets==5.14.3
    # via comm
    # via ipykernel
    # via ipython
    # via jupyter-client
    # via jupyter-core
    # via jupyter-events
    # via jupyter-server
    # via jupyterlab
    # via matplotlib-inline
    # via nbclient
    # via nbconvert
    # via nbformat
trove-classifiers==2025.5.9.12
    # via hatchling
types-python-dateutil==2.9.0.20250516
    # via arrow
typing-extensions==4.13.2
    # via anyio
    # via beautifulsoup4
    # via fastapi
    # via ipython
    # via langchain-core
    # via openai
    # via pydantic
    # via pydantic-core
    # via referencing
    # via sqlalchemy
    # via typing-inspect
    # via typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic
    # via pydantic-settings
tzdata==2025.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via sportega-rockets
wcwidth==0.2.13
    # via prompt-toolkit
webcolors==24.11.1
    # via jsonschema
webencodings==0.5.1
    # via bleach
    # via tinycss2
websocket-client==1.8.0
    # via jupyter-server
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zstandard==0.23.0
    # via langsmith
