import logging
from pathlib import Path
import os
import pandas as pd

import os
from sportega_rockets.config_manager import ConfigManager

from langchain.chat_models import init_chat_model
from langchain.prompts import PromptTemplate
import concurrent.futures

from sportega_rockets.logger import logger
from sportega_rockets.tools.query_responser_pack.description_finder import DescriptionFinder
from pathlib import Path

PRODUCT_NAME = "tenis > tréninkové pomůcky"

catalog_config_abs = Path(__file__).resolve().parent.parent.parent / "data" / "config" / "catalog-tables.yml"
CONNECTION_STRING = os.environ.get("CONNECTION_STRING")
config_manager = ConfigManager()
config_manager.configure(
    connection_string=CONNECTION_STRING,
    catalog_config_path=str(catalog_config_abs),
)
catalogs = config_manager.get_catalogs()
catalog_descriptions = catalogs[PRODUCT_NAME].descriptions
catalog_products = catalogs[PRODUCT_NAME].products

# Get BM25 index from ConfigManager if available
bm25_index = None
if PRODUCT_NAME in catalogs:
    bm25_index = catalogs[PRODUCT_NAME].bm25_index
else:
    logger.warning(f"BM25 index for {PRODUCT_NAME} not available")


def process_rag_questions():

    domain = config_manager.config["domain_settings"][PRODUCT_NAME]

    rag = DescriptionFinder(
        agent=None,
        catalog_descriptions=catalog_descriptions,
        catalog=catalog_products,
        product_name=domain["product_name"],
        logger=logger.bind(domain=PRODUCT_NAME),
        bm25_index=bm25_index,
    )

    # Load test cases from rag_questions.csv
    csv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, "component tests/all_product_questions.csv"))
    df = pd.read_csv(csv_path)
    if "user_input" not in df.columns:
        raise ValueError("rag_questions.csv must have a 'user_input' column!")
    results_list = []

    # Limit to first 3 rows for testing
    # df = df.head(5)

    llm = init_chat_model("gpt-4.1", model_provider="openai", temperature=0)
    prompt_template = PromptTemplate.from_template(
        """
        Your task is to compare the question and the actual description of a product that was matched to the question.
        If the product description matches the question, return "Yes", otherwise return "No". \n
        The product must match every single requirement of the question. \
        If there is a requirement in a question, that is not mentioned in the product description, such result is ok, and return "Yes". \n
        After the Yes/No response, include the reasoning why you decided like that. \n
        Question: {user_input} \n
        Product description: {description_altered} \n
        """
    )

    def get_llm_response(item, user_input):
        description_altered = catalog_descriptions.loc[catalog_descriptions["id"] == item["id"], "description_cleaned"].iloc[0]
        prompt = prompt_template.format(user_input=user_input, description_altered=description_altered)
        try:
            return llm.invoke(prompt).content
        except Exception as e:
            logger.error(f"Error getting LLM response for item {item.get('id', 'N/A')}: {e}")
            return f"Error: {e}"

    for _, row in df.iterrows():
        question = row["user_input"]
        logger.info(f"Processing question: {question}")
        run_results = rag.find(question)
        top3 = run_results[:5] if isinstance(run_results, list) else []
        row_data = row.to_dict()
        for i, item in enumerate(top3):
            # Get the description_cleaned using the item's id
            description = catalog_descriptions.loc[catalog_descriptions["id"] == item["id"], "description_cleaned"].iloc[0]
            row_data[f"result_{i+1}"] = {"id": item["id"], "description_cleaned": description}  # Changed score to description_cleaned
        for j in range(len(top3), 5):
            row_data[f"result_{j+1}"] = {}

        # Prepare arguments for parallel execution
        items_to_process = top3
        user_inputs = [row["user_input"]] * len(items_to_process)

        llm_responses = [None] * 5
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            # Map the function to the items concurrently
            results = list(executor.map(get_llm_response, items_to_process, user_inputs))

            # Assign results back
            for i, response in enumerate(results):
                llm_responses[i] = response

        for i in range(5):
            row_data[f"top{i+1}_result"] = llm_responses[i]

        results_list.append(row_data)
    df_results = pd.DataFrame(results_list)
    output_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, "component tests/rag_results.csv"))
    df_results.to_csv(output_path, index=False)
    logger.info(f"Results saved to {output_path}")


def compute_rag_scores():
    """Load rag_results.csv and compute a score for each row based on top1/2/3 results."""
    csv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, "component tests/rag_results.csv"))
    df = pd.read_csv(csv_path)

    def point_for_row(row):
        points = 0
        for i in range(1, 4):
            res = row.get(f"top{i}_result")
            if isinstance(res, str) and "no" in res.lower()[:3]:
                points -= 1
        return points

    df["points"] = df.apply(point_for_row, axis=1)
    # save the df to csv
    output_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, "component tests/rag_results_scored.csv"))
    df.to_csv(output_path, index=False)
    return df


if __name__ == "__main__":

    process_rag_questions()
    # compute_rag_scores()
