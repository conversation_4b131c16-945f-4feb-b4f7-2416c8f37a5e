#%% md
# other + list of categories
#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

#%%
df = pd.read_csv('categories_from_web_manual_flattened.csv',sep=';')
df
#%%
from pydantic import BaseModel
from typing import Literal, List

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist() + ['other']

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: List[Literal[tuple(categories)]]
#%%
OPENAI_API_KEY='*******************************************************************************************************************************************'
llm = ChatOpenAI(
            openai_api_key=OPENAI_API_KEY,
            temperature=0,
            model="gpt-4o",
        )
structured_llm = llm.with_structured_output(schema=Output, method="function_calling")

prompt = f"""\
Decide which categories from the following categories the text belongs to. \
If the text belongs to none of the categories, return 'other'. \
If the can belong to multiple categories, return list of all possible categories. \
The categories are: {categories}
The text is: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
question="jaka je nejdrazsi tenisova raketa?"
chain.invoke({"text": question}).category
#%%
question="jaka je dnes pocasi?"
chain.invoke({"text": question}).category
#%%
question="kolik máte modelů?"
chain.invoke({"text": question}).category
#%%
question="Jsou tyto pásky vodě odolné nebo se mohou poškodit při hře v dešti?"
chain.invoke({"text": question}).category
#%%
chain.invoke({"text": "Má tato páska reflexní prvky pro lepší viditelnost při hře za šera?"}).category
#%% md
vypada to, ze to nepomaha a funguje ve vysledku stejne, jako pri vyberu jedne kategorie
#%%
chain.invoke({"text": "Jaká láhev na nápoje je nejlehčí pro běh?"}).category
#%%
chain.invoke({"text": "prodas mi auto?"}).category
#%% md
tyhle jsou ale zase dobre
#%% md
zkusil jsem i 4o-mini, a 4o je asi lepsi a rychlost srovnatelna