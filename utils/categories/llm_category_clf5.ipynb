#%% md
# other + list of categories - spatne predikce jednokategorickeho clf
#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

#%%
df = pd.read_csv('categories_from_web_manual_flattened.csv',sep=';')
df
#%%
from pydantic import BaseModel
from typing import Literal, List

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist() + ['other']

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: List[Literal[tuple(categories)]]
#%%
OPENAI_API_KEY='*******************************************************************************************************************************************'
llm = ChatOpenAI(
            openai_api_key=OPENAI_API_KEY,
            temperature=0,
            model="gpt-4o-2024-08-06",
            streaming=False,
        )
structured_llm = llm.with_structured_output(schema=Output, method="json_schema", strict=True)

prompt = f"""\
Decide which categories from the following categories the text belongs to. \
If the text belongs to none of the categories, return 'other'. \
If the can belong to multiple categories, return list of all possible categories.
The categories are: {categories}
The text is: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
question="jaka je nejdrazsi tenisova raketa?"
chain.invoke({"text": question}).category
#%%
question="jaka je dnes pocasi?"
chain.invoke({"text": question}).category
#%%
question="kolik máte modelů?"
chain.invoke({"text": question}).category
#%%
question="Jsou tyto pásky vodě odolné nebo se mohou poškodit při hře v dešti?"
chain.invoke({"text": question}).category
#%%
chain.invoke({"text": "Má tato páska reflexní prvky pro lepší viditelnost při hře za šera?"}).category
#%% md
vypada to, ze to nepomaha a funguje ve vysledku stejne, jako pri vyberu jedne kategorie
#%%
chain.invoke({"text": "Jaká láhev na nápoje je nejlehčí pro běh?"}).category
#%%
chain.invoke({"text": "prodas mi auto?"}).category
#%%
df_q = pd.read_csv('questions_with_categories_errors_anotated.csv')
df_q
#%%
df_q = df_q[df_q.error=='predicted'].copy()
#%%
df_q['predicted_list'] = df_q['user_input'].apply(lambda x: chain.invoke({"text": x}).category)
#%%
pd.set_option('display.max_colwidth', None)
pd.set_option('display.max_rows', None)
#%%
df_q[['user_input','category','category_predicted','predicted_list']]
#%%
df_q['pokryto_seznamem'] = df_q.apply(lambda x: x['category'] in x['predicted_list'], axis=1)
#%%
df_q['pokryto_seznamem'].value_counts()
#%% md
tedy seznam vyresi 2/3 chybnych klasifikaci
#%% md
pokud budu predpokladat, ze co bylo spravne u klasifikace s jednim vysledkem by bylo i se seznamem mam 37 z 5000 pripadu spatne = 7 promile.. pro ted to staci