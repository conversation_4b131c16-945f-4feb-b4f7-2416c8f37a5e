#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

#%%
df = pd.read_csv('categories_from_web_manual_flattened.csv',sep=';')

#%%
from pydantic import BaseModel
from typing import Literal, List

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist() + ['other']

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: List[Literal[tuple(categories)]]
#%%
OPENAI_API_KEY='*******************************************************************************************************************************************'
llm = ChatOpenAI(
            openai_api_key=OPENAI_API_KEY,
            temperature=0,
            model="gpt-4o-2024-08-06",
            streaming=False,
        )
structured_llm = llm.with_structured_output(schema=Output, method="function_calling", strict=True)

prompt = f"""\
Decide which categories from the following categories the text belongs to. \
If the text belongs to none of the categories, return 'other'. \
If the can belong to multiple categories, return list of all possible categories.
The categories are: {categories}
The text is: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
import os
import pandas as pd

# List of file names
file_names = [f"conv_to_clf/conv{i}.csv" for i in range(1, 9)]


#%%
#from langchain.schema import HumanMessage, AIMessage

# Initialize an empty list to store the lists of messages
all_messages = []

# Iterate over each file in file_names
for file in file_names:
    # Read the file into a DataFrame
    df = pd.read_csv(file)
    
    # Create a list of messages for the current file
    messages = [
        f"HumanMessage(content={row['text']})" if row['ai/user'] == 'user' else f"AIMessage(content={row['text']})"
        for _, row in df.iterrows()
    ]
    
    # Append the list of messages to the main list
    all_messages.append(messages)

# all_messages now contains the desired structure
#%%
flattened_messages = [','.join(sublist) for sublist in all_messages]
flattened_messages
#%%
df = pd.DataFrame({'conv': flattened_messages})
df
#%%
# Load the categories.csv file
categories_df = pd.read_csv('conv_to_clf/categories.csv')

# Ensure the categories_df has the same index as df
categories_df = categories_df.set_index(df.index)

# Add the 'category' column to df
df['category'] = categories_df['category']
df
#%%
df['category_predicted'] = df.conv.apply(lambda x: chain.invoke({"text": x}).category)
#%%
pd.set_option('display.max_colwidth', None)
pd.set_option('display.max_rows', None)
#%%
df
#%% md
docela funguje, ale je videt, ze to zaznamenava kategorie, co se vyskytuji v konverzaci a ne posledni relevantni...