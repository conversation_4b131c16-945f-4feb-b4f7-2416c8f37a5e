#%%
import numpy as np
import dotenv
import os

dotenv.load_dotenv()

CONNECTION_STRING="postgresql://dev:<EMAIL>/sportega"
OPENAI_KEY=os.environ["OPENAI_API_KEY"]
#%%
import pandas as pd
import xml.etree.ElementTree as ET

# Načtení XML souboru
tree = ET.parse('sportega_rss_cely_katalog.xml')
root = tree.getroot()

# Inicializace seznamu pro uložení dat
data = []

# Iterace přes všechny položky <item>
for item in root.findall('.//item'):
    item_data = {}
    params = []
    for child in item:
        if child.tag == 'PARAM':
            param_name = child.find('PARAM_NAME').text
            param_value = child.find('VAL').text if child.find('VAL') is not None else None
            if param_name in params:
                print(f'id {item_data["id"]}')
                print(f'old {item_data[param_name]}')
                print(f'new {param_value}')
            item_data[param_name] = param_value 
            params.append(param_name)           
        else:
            item_data[child.tag] = child.text
    data.append(item_data)

# Vytvoření DataFrame z extrahovaných dat
df = pd.DataFrame(data)
#%%
df.columns.to_list()
#%%
df.rename(columns={'{http://base.google.com/ns/1.0}image_link': 'image_link',
       '{http://base.google.com/ns/1.0}price': 'price',
       '{http://base.google.com/ns/1.0}availability': 'availability',
       '{http://base.google.com/ns/1.0}brand': 'brand',
       '{http://base.google.com/ns/1.0}color': 'color',
       '{http://base.google.com/ns/1.0}material': 'material',
       '{http://base.google.com/ns/1.0}product_type': 'product_type',
       '{http://base.google.com/ns/1.0}item_group_id': 'item_group_id'}, inplace=True)
#%%
df["product_type"].value_counts()
#%%
categories = df[df["product_type"].notna()].product_type.unique().tolist()
#%%
categories
#%%
categories.sort()
#%%
categories = [category.replace("Sportega > ", "") for category in categories]
#%%
import csv

# Uložení categories jako CSV soubor
with open('categories.csv', 'w', newline='', encoding='utf-8') as file:
    writer = csv.writer(file)
    writer.writerow(['Category'])
    for category in categories:
        writer.writerow([category])