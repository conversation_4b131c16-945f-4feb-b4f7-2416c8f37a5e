import random
from pathlib import Path
import argparse
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain.chat_models import init_chat_model
import dotenv
import os

from sportega_rockets.config_manager import ConfigManager

dotenv.load_dotenv()

DEFAULT_PRODUCT_NAME = "tenis > obuv"
DEFAULT_QUESTION_TYPE = "filter"  # "filter", "general", "knowledge", "title", "description"
DEFAULT_NUMBER_OF_QUESTIONS = 10
LLM_NAME = "gpt-4.1"  # Nepouzivat Gemini, protoze genruje silene slozity vety
LLM_PROVIDER = "openai"
LLM_TEMPERATURE = 0

# Access product catalog
script_dir = Path(__file__).resolve().parent
catalog_config_abs = script_dir / ".." / ".." / "data" / "config" / "catalog-tables.yml"
CONNECTION_STRING = os.environ.get("CONNECTION_STRING")
config_manager = ConfigManager()
config_manager.configure(
    connection_string=CONNECTION_STRING,
    catalog_config_path=str(catalog_config_abs),
)
catalogs = config_manager.get_catalogs()
product_catalog = catalogs[DEFAULT_PRODUCT_NAME].products


def get_random_descriptions(n=3):
    """Get n random descriptions from the product catalog."""
    if "description" not in product_catalog.columns:
        return ["No descriptions available in the product catalog."]

    # Filter out empty descriptions
    valid_descriptions = product_catalog["description"].dropna().astype(str)
    if valid_descriptions.empty:
        return ["No valid descriptions available in the product catalog."]

    # Get n random descriptions (or fewer if not enough available)
    sample_size = min(n, len(valid_descriptions))
    random_indices = random.sample(range(len(valid_descriptions)), sample_size)
    return [valid_descriptions.iloc[i] for i in random_indices]


# Templates for different question types
TEMPLATES = {
    "filter": """You are a customer of a webstore. Your task is to generate various questions about one given product type.
    Each generated question you want a new product with new parameters and playstyle
    Generate exactly {number_of_examples} questions about {category}.
    Specify some category attributes and your preferences as a customer. Return only the questions as strings. Nothing else.
    Do not make your request too complicated. Specify only 1-2 preferences. You can be specific and select numerical or categorical values.
    The questions must be in English language. Do not include numbers or " characters in your response.""",
    "general": """You are a customer of a webstore. Your task is to generate various questions about the product catalog
    Generate exactly {number_of_examples} questions about {category}.
    Response to these questions are usualy, number(s) or list of parameters. Be creative.
    Return only the questions as strings. Nothing else.
    The questions must be in English language. Do not include numbers or quotes characters in your response.
    EXAMPLES:
    What is the most expensive ... 
    How many product types ... 
    What is the heaviest (product).
    What are the unique product parameters
    You can also select a very specific attrubite requirement. And ask the same questions above but this time with that attribute in mind.
    EXAMPLES:
    What is the average (product parameter) of the product that has (product value)
    What is the most expensive (product) with (attribute) co máte.
    How many (products) with (attribute) for (customer_type)?""",
    "knowledge": """You are a customer of a webstore. Your task is to generate various questions about the sports equipment
    Generate exactly {number_of_examples} questions about {category}.
    Ask anything, as long as it is related to the category. Be creative.
    Return only the questions as strings. Nothing else.
    The questions must be in English language. Do not include numbers or quotes characters in your response.""",
    "title": """generate {number_of_examples} product titles of this {category}. Generate them both with and also without the actual category name. 
    Each time use different a category. EXAMPLE: Tenisový míček Dunlop Fort Clay court. Return only the titles, without any special characers, quotes and numbers. 
    Never include the headers With Category/ Without Category""",
    "description": """You are a customer of a webstore. Your task is to generate various questions about {category} products.
    Generate exactly {number_of_examples} questions based on the following product descriptions:
    {descriptions}
    Each question should be related to one of these descriptions. You can ask about 1 parameter mentioned in one description.
    Formulate the question as if you were searching for a product. eg. I want a product that..., or Find..., or Do you offer...
    Return only the questions as strings. Nothing else.
    The questions must be in English language. Do not include numbers or quotes characters in your response.""",
}


def generate_product_questions(category: str, number_of_examples: int, question_type: str) -> pd.DataFrame:
    """Generate a specified number of questions about a given category of a specific type and return them as a DataFrame."""

    # Validate question type
    if question_type not in TEMPLATES:
        raise ValueError(f"Invalid question type: {question_type}. Must be one of {list(TEMPLATES.keys())}")

    # Generate questions for the specified template type
    results = []

    # Select LLM model
    llm = init_chat_model(model=LLM_NAME, model_provider=LLM_PROVIDER, temperature=LLM_TEMPERATURE)

    # Create and run prompt chain
    template = PromptTemplate.from_template(TEMPLATES[question_type])
    chain = template | llm | StrOutputParser()

    # For description type, get random descriptions from the product catalog
    if question_type == "description":
        random_descriptions = get_random_descriptions(DEFAULT_NUMBER_OF_QUESTIONS)  # Get 3 random descriptions
        descriptions_text = "\n\n".join(random_descriptions)
        output = chain.invoke({"category": category, "number_of_examples": number_of_examples, "descriptions": descriptions_text})
    else:
        output = chain.invoke({"category": category, "number_of_examples": number_of_examples})

    # Process questions
    questions = [q for q in output.split("\n") if q.strip()]

    # Add to results
    for question in questions:
        results.append({"user_input": question, "type": question_type, "category": category})

    print(f"Generated {len(questions)} questions for {category} of type {question_type}.")

    return pd.DataFrame(results)


def save_to_csv(data: pd.DataFrame, product_name: str, number_of_examples: int) -> None:
    """Saves data to a single CSV file. Appends if file exists."""
    filename = "all_product_questions.csv"
    csv_dir = Path("")
    csv_dir.mkdir(parents=True, exist_ok=True)
    csv_path = csv_dir / filename

    # Append or create new file
    data.to_csv(csv_path, mode="a" if csv_path.exists() else "w", header=not csv_path.exists(), index=False)
    print(f"Generated {number_of_examples} questions for {product_name} and saved to {filename}")


# some models generate unnecesary characters, so filter them out
def filter_results_csv():
    # Read the CSV file
    file_path = "all_product_questions.csv"
    df = pd.read_csv(file_path)

    # Fill NaN values with empty strings to avoid TypeError with str.contains()
    df["user_input"] = df["user_input"].fillna("")

    # Filter out rows containing "python" or "question" or ']' or '```' in user_input column
    df = df[~df["user_input"].str.contains("python|question|\\]|```", case=False, na=False)]

    # If the first 3 characters are number, dot, space .. then delete these 3 characters
    df["user_input"] = df["user_input"].str.replace(r"^\d+\.\s+", "", regex=True)

    # Delete entire row if user input contains ** and **
    df = df[~df["user_input"].str.contains(r"\*\*.*\*\*", regex=True)]

    # Save the filtered data back to the same file
    df.to_csv(file_path, index=False)
    print(f"Filtered results saved to {file_path}.")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate product questions of a specific type.")
    parser.add_argument(
        "--type",
        "-t",
        choices=["filter", "general", "knowledge", "title", "description"],
        default=DEFAULT_QUESTION_TYPE,
        help="Type of questions to generate (filter, general, knowledge, title, or description)",
    )
    parser.add_argument("--product", "-p", default=DEFAULT_PRODUCT_NAME, type=str, help="Name of the product")
    parser.add_argument("--number", "-n", type=int, default=DEFAULT_NUMBER_OF_QUESTIONS, help="Number of examples to generate")

    args = parser.parse_args()

    # Generate questions
    result = generate_product_questions(args.product, args.number, args.type)

    # Save to CSV
    save_to_csv(result, args.product, args.number)

    # Filter results
    filter_results_csv()
