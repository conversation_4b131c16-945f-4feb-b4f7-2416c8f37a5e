#%%
import pandas as pd

#%%
import pandas as pd

def cols_cmp(category):
    # Vytvoření přehledu sloupců s alespoň jednou nenulovou hodnotou pro každou podkategorii
    columns_by_category = {}
    category_by_product_type = category.groupby('product_type')
    for cat, df_cat in category_by_product_type:
        non_null_cols = df_cat.columns[df_cat.notnull().any()].tolist()
        columns_by_category[cat] = set(non_null_cols)

    # Výpis srovnání sloupců mezi kategoriemi

    all_columns = sorted(set.union(*columns_by_category.values()))
    comparison_df = pd.DataFrame(
        {cat: [col in cols for col in all_columns] for cat, cols in columns_by_category.items()},
        index=all_columns
    )
    comparison_df = comparison_df.astype(int)  # 1 = sloupec je v kategorii, 0 = není

    return comparison_df
#%%
from collections import defaultdict

# col_cal_map obsahuje DataFrame, kde index jsou názvy sloupců a sloupce jsou názvy kategorií, hodnoty 1/0
# Potřebujeme najít skupiny kategorií, které mají stejnou množinu sloupců (tj. stejný vektor 1/0 přes všechny sloupce)

def group_categories_by_columns(col_cal_map):
    # Transponujeme, aby řádky byly kategorie a sloupce sloupce
    tdf = col_cal_map.T
    # Pro každou kategorii vytvoříme tuple z vektoru 1/0 (přes všechny sloupce)
    col_signature = tdf.apply(lambda row: tuple(row.values), axis=1)
    # Skupiny: klíč je tuple (signature), hodnota je seznam kategorií
    groups = defaultdict(list)
    for cat, sig in col_signature.items():
        groups[sig].append(cat)
    # Výstup: seznam skupin (každá skupina je seznam kategorií se stejnou množinou sloupců)
    return list(groups.values())
#%%
def variant_columns(df):
    # Najdi sloupce, kde existuje alespoň jedna skupina item_group_id s více různými hodnotami
    inconsistent_cols = []
    group = df.groupby('item_group_id')
    for col in df.columns:
        if col == 'item_group_id':
            continue
        nunique_per_group = group[col].nunique(dropna=False)
        if (nunique_per_group > 1).any():
            inconsistent_cols.append(col)
    return inconsistent_cols
#%%
df = pd.read_csv('tenis_bez_raket_vypletu_micku.csv',low_memory=False)
#%% md
# Doplnky
#%%
df[df['product_type'].str.contains('doplňky',na=False,case=False)]['product_type'].unique()
#%%
df = df[df['product_type'].str.contains('doplňky',na=False,case=False)]
df.shape[0]
#%%
df.drop(columns=['id', 'description', 'link', 'image_link', 'availability',
       'price', 'brand', 'condition', 'stock', 'rank'], inplace=True)  # Odstranění sloupce 'product_type' pro porovnání
#%%
variant_columns(df)
#%% md
## Vibrastopy
#%%
vibra = df[df['product_type'].str.contains('Vibrastopy', na=False)]
vibra = vibra.dropna(axis=1, how='all')
vibra.columns
#%%
cols = {}
cols['vibrastopy'] = vibra.columns.tolist()
cols['vibrastopy'].remove('product_type')
#%%
vibra['Počet ks v balení'].value_counts()
#%%
# Najdi duplicity v item_group_id ve vibrastopy
dups = vibra[vibra['item_group_id'].notnull()]
dups = dups[dups.duplicated('item_group_id', keep=False)]

# Porovnej řádky se stejným item_group_id
diffs = {}
for igid, group in dups.groupby('item_group_id'):
    # Porovnej hodnoty po sloupcích
    diff_cols = []
    for col in group.columns:
        if group[col].nunique(dropna=False) > 1:
            diff_cols.append(col)
    diffs[igid] = diff_cols

diffs
#%%
vibra['item_group_id'].value_counts()
#%%
vibra[vibra['item_group_id'].notna()]
#%% md
Nevime, cim se lisi varianty v vibrastopech... Dle webu je varianta barva. Je to jen u dvou, tak varianty asi neresit.
#%%
vibra.shape[0]
#%% md
## Ostatni
#%%
ostatni = df[df['product_type'].str.contains('Ostatní', na=False)]
ostatni = ostatni.dropna(axis=1, how='all')
cols['ostatni'] =  ostatni.columns.tolist()
cols['ostatni'].remove('product_type')
ostatni.columns
#%% md
Obsahuje jednu cepici.. proc?? Obsahuje i jeden velkej reklamni mic
#%%
ostatni.shape[0]
#%%
ostatni['item_group_id'].value_counts()
#%%
ostatni[ostatni['item_group_id'].notnull()]
#%% md
Varianty tedy neresit? Dle weby je varianta velikost.
#%%
ostatni
#%% md
## Ochranne pasky
#%%
op = df[df['product_type'].str.contains('Ochranné pásky', na=False)]
op = op.dropna(axis=1, how='all')
cols['op'] = op.columns.tolist()
cols['op'].remove('product_type')
op.columns
#%%
op.shape[0]
#%% md
## Popisovace strun
#%%
ps = df[df['product_type'].str.contains('Popisovače', na=False)]
ps = ps.dropna(axis=1, how='all')
cols['ps'] =  ps.columns.tolist()
cols['ps'].remove('product_type')
ps.columns
#%%
ps.shape[0]
#%%
ps['item_group_id'].value_counts()
#%%
ps[ps['item_group_id'].notnull()]
#%% md
Dle webu to vypada, ze varianta je barva.
#%% md
## Lahve
#%%
lahve = df[df['product_type'].str.contains('Láhve', na=False)]
lahve = lahve.dropna(axis=1, how='all')
cols['lahve'] =  lahve.columns.tolist()
cols['lahve'].remove('product_type')
lahve.columns
#%%
lahve.shape[0]
#%% md
NA webu je 13.5. lahvi 8, z toho 2 ty nize
#%%
lahve
#%% md
## Doplnky bez podkategorie
#%%
doplnky = df[df['product_type']=='Sportega > Tenis > Doplňky']
doplnky = doplnky.dropna(axis=1, how='all')
cols['doplnky'] =  doplnky.columns.tolist()
cols['doplnky'].remove('product_type')
doplnky.columns
#%%
doplnky.shape[0]
#%%
doplnky
#%% md
Vsechno ma svoji vlastni kategorii, proc je v doplnkach??  Jsou jen 3 a navic patri jinam, neresil bych.
#%% md
## Dovyvazovaci pasky
#%%
dp = df[df['product_type'].str.contains('Dovažovací', na=False)]
dp = dp.dropna(axis=1, how='all')
cols['dp'] =  dp.columns.tolist()
cols['dp'].remove('product_type')
dp.columns
#%%
dp.shape[0]
#%% md
## Obaly
#%%
obaly = df[df['product_type'].str.contains('Obaly', na=False)]
obaly = obaly.dropna(axis=1, how='all')
cols['obaly'] =  obaly.columns.tolist()
cols['obaly'].remove('product_type')

obaly.columns
#%%
obaly.shape[0]
#%% md
## Klicenky
#%%
klicenky = df[df['product_type'].str.contains('Klíčenky', na=False)]
klicenky = klicenky.dropna(axis=1, how='all')
cols['klicenky'] =  klicenky.columns.tolist()
cols['klicenky'].remove('product_type')
klicenky.columns
#%%
klicenky.shape[0]
#%%
pd.set_option('display.max_colwidth', None)
#%%
cols_df_simple = pd.DataFrame([
    {'kategorie': k, 'parametry': v} for k, v in cols.items()
])
cols_df_simple
#%% md
KDe je item_group_id, musi to mit specificky variant_column => musi to byt extra kategorie nase. JEnze to maji i kategorie Doplnky bez podkat. a Ostatni... drzi to nejakou logiku?

Neni uplne pravd - pokud by vsechny kategorie mely stejnoy variant_column, nemusekly by to byt nase kategorie... JEnze stejny nemaji.
#%% md
Krome vibrastopu je vsechno pod 10, nebo nejvic jedno je 15 polozek - ma smysl to resit?
#%% md
TODO podkategorie, co maj dalsi podkategorie - omotavky , potitka,celenky,rucniky
#%% md
## Omotavky
#%%
omotavky = df[df['product_type'].str.contains('Omotávky', na=False)]
omotavky = omotavky.dropna(axis=1, how='all')
omotavky.columns
#%%
variant_columns(omotavky)
#%%
omotavky.shape[0]
#%%
omotavky.product_type.value_counts()
#%%
omv = omotavky[omotavky['product_type'].str.contains('Vrchní omotávky', na=False)]
omz = omotavky[omotavky['product_type'].str.contains('Základní omotávky', na=False)]
omv = omv.dropna(axis=1, how='all')
omz = omz.dropna(axis=1, how='all')
#%%
omv.columns
#%%
omz.columns
#%%
set(omv.columns).intersection(set(omz.columns))
#%%
set(omv.columns).difference(set(omz.columns))
#%%
set(omz.columns).difference(set(omv.columns))
#%% md
velky prinuk parametry, necham jednu kategorii
#%% md
## Potitka celenky rucniky
#%%
pcr = df[df['product_type'].str.contains('Potítka', na=False)]
pcr = pcr.dropna(axis=1, how='all')
pcr.columns
#%%
variant_columns(pcr)
#%%
pcr.shape[0]
#%%
pcr.product_type.value_counts()
#%%
pcr['Typ produktu'].value_counts()
#%%
potitka = pcr[pcr['product_type']=='Sportega > Tenis > Doplňky > Potítka, čelenky a ručníky > Potítka']
potitka = potitka.dropna(axis=1, how='all')
potitka.columns
#%%
celenky = pcr[pcr['product_type']=='Sportega > Tenis > Doplňky > Potítka, čelenky a ručníky > Čelenky']
celenky = celenky.dropna(axis=1, how='all')
celenky.columns
#%%
rucniky = pcr[pcr['product_type']=='Sportega > Tenis > Doplňky > Potítka, čelenky a ručníky > Ručníky']
rucniky = rucniky.dropna(axis=1, how='all')
rucniky.columns
#%%
prunik = set(potitka.columns).intersection(set(celenky.columns)).intersection(set(rucniky.columns))
prunik
#%%
set(potitka.columns).difference(set(prunik))
#%%
set(celenky.columns).difference(set(prunik))
#%%
set(rucniky.columns).difference(set(prunik))
#%%
cpcr = pcr[pcr['product_type']=='Sportega > Tenis > Doplňky > Potítka, čelenky a ručníky']
cpcr = cpcr.dropna(axis=1, how='all')   
cpcr.columns
#%%
cpcr
#%% md
blbe zarazene, neresil bych 
#%% md
jsou to dost nesourode veci, mely by to byut extra kategorie... potitek je dost, celenek je na hrane a rucniku je tak malo ze to ale nema smysl delat...