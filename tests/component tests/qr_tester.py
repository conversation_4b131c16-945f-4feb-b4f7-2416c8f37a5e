import os.path
import pprint
import argparse
from pathlib import Path
import pandas as pd
import dotenv
from loguru import logger

from sportega_rockets.config_manager import ConfigManager
from sportega_rockets.tools.query_responser_pack.query_responser import QueryResponser

# Load environment variables
dotenv.load_dotenv()

# Constants
PRODUCT_NAME = "tenis > rakety"  # Default product name for testing
CONNECTION_STRING = os.environ.get("CONNECTION_STRING")

def setup_environment():
    """Set up the environment and return necessary configurations."""
    script_dir = Path(__file__).resolve().parent
    catalog_config_abs = script_dir / ".." / ".." / "data" / "config" / "catalog-tables.yml"
    
    # Initialize config manager
    config_manager = ConfigManager()
    config_manager.configure(
        connection_string=CONNECTION_STRING,
        catalog_config_path=str(catalog_config_abs),
    )
    
    # Get catalog data
    catalogs = config_manager.get_catalogs()
    catalog = catalogs[PRODUCT_NAME]
    
    return {
        'catalog': catalog.products,
        'catalog_embeddings': catalog.embeddings,
        'catalog_descriptions': catalog.descriptions if hasattr(catalog, 'descriptions') else None,
        'domain': config_manager.config["domain_settings"][PRODUCT_NAME],
        'script_dir': script_dir
    }

def initialize_query_responser(config):
    """Initialize and return a QueryResponser instance."""
    domain = config['domain']
    
    return QueryResponser(
        agent=None,  # No agent in testing
        catalog=config['catalog'],
        catalog_name=PRODUCT_NAME,
        catalog_embeddings=config['catalog_embeddings'],
        catalog_descriptions=config['catalog_descriptions'],
        model="gpt-4",  # or your preferred model
        logger=logger.bind(domain=PRODUCT_NAME),
        flowid="test_flow",
        last_query=None,
        last_code=None,
        product_name=PRODUCT_NAME,
        search_columns=domain["search_columns"].split(",") if "search_columns" in domain else [],
        fetch_columns=domain["fetch_columns"].split(",") if "fetch_columns" in domain else [],
        variant_column=domain.get("variant_column"),
        categorical_columns=domain.get("categorical_columns", "").split(",") if "categorical_columns" in domain else [],
        color_column=domain.get("color_column"),
        table_specific_instructions=domain.get("table_specific_instructions", ""),
        npf_configuration=domain.get("npf_config"),
    )

def process_queries(qr, input_file, output_file):
    """Process queries from input file and save results to output file."""
    # Read input data
    try:
        df = pd.read_csv(input_file)
    except Exception as e:
        logger.error(f"Error reading input file {input_file}: {e}")
        return
    
    results = []
    
    # Process each row
    for _, row in df.iterrows():
        try:
            query = row["user_input"] if "user_input" in row else row.get("query", row.get("queries", ""))
            
            # Run the query through QueryResponser
            logger.info(f"Processing query: {query}")
            response = qr.response(query)
            
            # Store results
            result = row.to_dict()
            result["response"] = pprint.pformat(response)
            results.append(result)
            
        except Exception as e:
            logger.error(f"Error processing query {row.get('query', '')}: {e}")
            result = row.to_dict()
            result["error"] = str(e)
            results.append(result)
    
    # Save results
    results_df = pd.DataFrame(results)
    results_df.to_csv(output_file, index=False)
    logger.info(f"Results saved to {output_file}")

def main():
    # Set up argument parsing
    parser = argparse.ArgumentParser(description='Test QueryResponser with input queries.')
    parser.add_argument('--input', type=str, default='qr_test_questions.csv',
                       help='Input CSV file with test queries in "user_input" column (default: qr_test_questions.csv)')
    parser.add_argument('--output', type=str, default='qr_test_results.csv',
                       help='Output CSV file for results (default: qr_test_results.csv)')
    args = parser.parse_args()
    
    # Set up environment and initialize QueryResponser
    config = setup_environment()
    qr = initialize_query_responser(config)
    
    # Process queries
    input_file = Path(config['script_dir']) / args.input
    output_file = Path(config['script_dir']) / args.output
    
    process_queries(qr, input_file, output_file)

if __name__ == "__main__":
    main()
