import dotenv

dotenv.load_dotenv()

from pathlib import Path
import argparse
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain.chat_models import init_chat_model
from sportega_rockets.config_manager import ConfigManager
from openai import OpenAI
from sportega_rockets.logger import logger
import os
import uuid

from sportega_rockets.tools.query_responser_pack.data_responser import DataResponser


# Default parameters
NUMBER_OF_QUESTIONS = 10
LLM_NAME = "gpt-4.1"
LLM_PROVIDER = "openai"
LLM_TEMPERATURE = 0
PRODUCT_NAME = "tenis > v<PERSON><PERSON><PERSON> do bot"

OPENAI_CLIENT = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

script_dir = Path(__file__).resolve().parent
catalog_config_abs = script_dir / ".." / ".." / "data" / "config" / "catalog-tables.yml"

CONNECTION_STRING = os.environ.get("CONNECTION_STRING")
config_manager = ConfigManager()
config_manager.configure(
    connection_string=CONNECTION_STRING,
    catalog_config_path=str(catalog_config_abs),
)
catalogs = config_manager.get_catalogs()
product_catalog = catalogs[PRODUCT_NAME].products


def get_query(question):
    """Get the query from the question using the HistoryReactAgent object."""

    # Rest of the function...
    flowid = str(uuid.uuid4())
    domain = config_manager.config["domain_settings"][PRODUCT_NAME]

    query_responser = DataResponser(
        agent=None,
        catalog=product_catalog,
        catalog_name=PRODUCT_NAME,
        model="gpt-4.1",
        logger=logger.bind(domain=PRODUCT_NAME),
        flowid=flowid,
        last_query="",
        last_code="",
        product_name=domain["product_name"],
        search_columns=domain["search_columns"].split(",") + ["id", "item_group_id"],
        fetch_columns=domain["fetch_columns"].split(",") + ["id", "item_group_id"],
        variant_column=domain["variant_column"],
        categorical_columns=domain["categorical_columns"].split(","),
        color_column=domain["color_column"],
        table_specific_instructions=domain["table_instructions"],
    )

    message_prompt = query_responser.system_first_message_filter
    print(f"System prompt: {message_prompt}")
    prompt = query_responser._create_first_try_prompt(message_prompt)
    chain = prompt | query_responser.llm_with_tools | query_responser.parser
    res = chain.invoke({"question": f"{question}"})
    print(f" Question {question} generated query {res['query']}")
    return res["query"]


def get_catalog_columns():
    """Get the column names from the product catalog."""

    return list(product_catalog.columns)


def get_column_unique_values(columns):
    """
    Get up to 8 unique values for each column in the product catalog.
    For numerical columns, return the range instead.
    """
    column_values = {}

    for column in columns:
        # Skip if column doesn't exist in the dataframe
        if column not in product_catalog.columns:
            continue

        # Check if column is numerical
        if pd.api.types.is_numeric_dtype(product_catalog[column]):
            # For numerical columns, get min and max to create a range
            min_val = product_catalog[column].min()
            max_val = product_catalog[column].max()
            column_values[column] = f"range: {min_val} to {max_val}"
        else:
            # For non-numerical columns, get up to 8 unique values
            unique_vals = product_catalog[column].dropna().unique()
            # Take up to 8 values
            sample_vals = unique_vals[:15] if len(unique_vals) > 15 else unique_vals
            # Convert to list of strings
            sample_vals = [str(val) for val in sample_vals]
            column_values[column] = ", ".join(sample_vals)

    return column_values


def generate_question_template(columns, column_values):
    """Generate a template for questions based on product catalog columns and their unique values."""
    column_list = ", ".join(columns)

    # Create a formatted string with column names and their unique values
    column_values_text = ""
    for column in columns:
        if column in column_values:
            column_values_text += f"\n    - {column}: {column_values[column]}"

    print(f"column_values_text: {column_values_text}")

    template = f"""You are a customer of a webstore. Your task is to generate various questions about {PRODUCT_NAME}.

    The product catalog has the following columns: {column_list}

    Here are some unique values for each column:{column_values_text}


    Generate exactly {NUMBER_OF_QUESTIONS} different questions, each asking to find or filter products, BUT in each question you must specify 1 to 3 attribute values that do NOT exist in the product catalog.
    - For categorical attributes, use values not present in the catalog.
    - For numerical attributes, ask for values higher than, lower than, or outside the range present in the catalog 
    - You may combine up to 3 non-existent attributes in one query. Each question must refer to different attribute/value combinations.

    Return only the questions as strings, one per line.
    The questions must be in English. Do not include numbers or quotation marks in your response. Do not use any numbering, bullet points, or special characters. Be specific and unambiguous.
    **Example behaviors you might follow:**
    - Ask for product of a brand or material not in the catalog.
    - Inquire about balls with a price much higher or lower than recorded values.
    - Request a combination of two or three attributes, all of which do not exist in the database.

    This prompt is designed to help test catalog search behaviors for missing or non-existent values.
    """

    return template


def generate_product_questions(number_of_questions: int) -> pd.DataFrame:
    """Generate a specified number of questions about products based on catalog columns."""

    # Get fetch_columns from domain settings
    domain = config_manager.config["domain_settings"][PRODUCT_NAME]
    raw_cols = [col.strip() for col in domain["fetch_columns"].split(",")]

    # Exclude the title column if present
    fetch_columns = [c for c in raw_cols if c.lower() != "title"]

    # Get unique values for each column
    column_values = get_column_unique_values(fetch_columns)

    # Initialize LLM model
    llm = init_chat_model(model=LLM_NAME, model_provider=LLM_PROVIDER, temperature=LLM_TEMPERATURE)

    # Create and run prompt chain
    template = PromptTemplate.from_template(generate_question_template(fetch_columns, column_values))
    chain = template | llm | StrOutputParser()
    output = chain.invoke({"number_of_questions": number_of_questions})

    # Process questions
    questions = [q for q in output.split("\n") if q.strip()]

    # Create results dataframe
    results = []
    print(f"Generating queries for each question...")
    for question in questions:
        if question and question.strip():  # Process only non-empty, non-whitespace questions
            try:
                query = get_query(question)
                results.append({"question": question, "queries": query})
            except Exception as e:
                print(f"Error generating query for question '{question}': {e}")
                results.append({"question": question, "queries": None})  # Append None or an error placeholder
        else:
            results.append({"question": question, "queries": None})  # Append None for empty or whitespace-only questions

    return pd.DataFrame(results)


def save_to_csv(data: pd.DataFrame, filename: str) -> None:
    """Saves data to a CSV file. Appends if file exists."""
    csv_dir = Path("")
    csv_dir.mkdir(parents=True, exist_ok=True)
    csv_path = csv_dir / filename

    # Append or create new file
    data.to_csv(csv_path, mode="a" if csv_path.exists() else "w", header=not csv_path.exists(), index=False)
    print(f"Generated {len(data)} questions and saved to {filename}.")


def filter_results_csv(file_path):
    """Filter out unwanted content from the results CSV."""
    # Read the CSV file
    df = pd.read_csv(file_path)

    # Fill NaN values with empty strings to avoid TypeError with str.contains()
    df["question"] = df["question"].fillna("")

    # Filter out rows containing unwanted patterns
    df = df[~df["question"].str.contains("python|example|\\]|```", case=False, na=False)]

    # If the first 3 characters are number, dot, space .. then delete these 3 characters
    df["question"] = df["question"].str.replace(r"^\d+\.\s+", "", regex=True)

    # Delete entire row if question contains ** and **
    df = df[~df["question"].str.contains(r"\*\*.*\*\*", regex=True)]

    # Save the filtered data back to the same file
    df.to_csv(file_path, index=False)
    print(f"Filtered results saved to {file_path}.")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate questions about products from catalog")
    parser.add_argument(
        "--num", type=int, default=NUMBER_OF_QUESTIONS, help=f"Number of questions to generate (default: {NUMBER_OF_QUESTIONS})"
    )
    parser.add_argument("--output", type=str, default="npf_questions.csv", help="Output CSV filename (default: npf_questions.csv)")
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()

    # Generate questions and their queries
    print(f"Generating {args.num} questions about products...")
    result = generate_product_questions(args.num)

    # Save results
    save_to_csv(result, args.output)
    filter_results_csv(args.output)

    print(f"Successfully generated questions with queries and saved to {args.output}.")
    print("Done!")
