#%% md
# Analyze the logs
#%%
connection_string: str = "postgresql://dev:<EMAIL>:5432/sportega"
#%%
import pandas as pd
from datetime import datetime

# Add 5 min on each side of the interval - sometimes the log arrive a bit later
start_time = "2025-02-02"
end_time = "2025-02-05"

sql =  (
    f"SELECT * FROM log WHERE time > '{start_time}' AND time < '{end_time}'"
)

print("Loading logs using...")
print(sql)
df = pd.read_sql(sql, connection_string)

if len(df) == 0:
    raise Exception("Failed to retreive logs from DB")
#%%
pd.set_option('display.max_colwidth', None)
#%%
df.extra
#%%
from collections import defaultdict
from copy import copy
import pandas as pd
import json

all_event_types = [
    ("on_agent_action", "Both final answer and a parse-able action"),
    ("on_agent_action", "Invalid Format: Missing Final Answer"),
    ("on_agent_action", "Invalid Format: Missing Action Input"),
    ("on_agent_action", "Instant action"),
    ("on_agent_finish", "(finish) Missing Thought"),
    ("on_agent_finish", "Instant Final Answer"),
    ("on_agent_finish", "Final Answer"),
    ("on_agent_finish", "Timeout"),
    ("-", "Error"),
    ("-", "Warning"),
    ("-", "Log message")
]


def analyze_logs(df, flow_id=None):
    if flow_id is not None:
        df = df[df.extra.str.contains(flow_id)]

    events = []

    total_calls = defaultdict(int)

    def collect(row):

        row = copy(row).to_dict()

        events.append(("-", "Log message", flow_id, row))

        if row["level"] == "ERROR":
            events.append(("-", "Error", flow_id, row))

        if row["level"] == "WARNING":
            events.append(("-", "Warning", flow_id, row))

        if row["function"] not in ["on_agent_action", "on_agent_finish"]:
            return

        total_calls[row["function"]] += 1

        if row["function"] == "on_agent_action":

            # tool='_Exception' tool_input='Invalid or incomplete response' log='Parsing LLM output produced both a final answer and a parse-able action:
            if "Parsing LLM output produced both a final answer and a parse-able action" in row["message"]:
                events.append(("on_agent_action", "Both final answer and a parse-able action", flow_id, row))

            # tool='_Exception' tool_input="Invalid Format: Missing 'Action:' after 'Thought:"
            if "Invalid Format: Missing 'Action:' after 'Thought" in row["message"]:
                events.append(
                    ("on_agent_action", "Invalid Format: Missing Final Answer", flow_id, row))  # this is correct

            # tool='_Exception' tool_input="Invalid Format: Missing 'Action Input:' after 'Action:'" log='Action: Ask the user about their experience level in tennis.'
            if "Invalid Format: Missing 'Action Input:' after 'Action" in row["message"]:
                events.append(("on_agent_action", "Invalid Format: Missing Action Input", flow_id, row))

            if "log='Action" in row["message"]:
               events.append(("on_agent_action", "Instant action", flow_id, row))
            # if row["message"].startswith("Action"):
            #     events.append(("on_agent_action", "Instant action", flow_id, row))

        if row["function"] == "on_agent_finish":
            if "Thought:" not in row["message"] and "Final Answer:" in row["message"]:
                events.append(("on_agent_finish", "(finish) Missing Thought", flow_id, row))

            if row["message"].startswith("Final Answer:"):
                events.append(("on_agent_finish", "Instant Final Answer", flow_id, row))

            if "Final Answer:" in row["message"]:
                events.append(("on_agent_finish", "Final Answer", flow_id, row))

            if len(row["message"].strip()) == 0:
                events.append(("on_agent_finish", "Timeout", flow_id, row))

    df.apply(collect, axis=1)

    if len(events) == 0:
        raise Exception(f"Failed to find any events for flowid {flow_id}")

    return pd.DataFrame(events, columns=["Function", "Event Type", "flow_id", "Log message data"]), total_calls


events = pd.DataFrame([], columns=["Function", "Event Type", "flow_id", "Log message data"])

total_calls = {}

print("Extracting info...")
flow_ids = []
for extra in df['extra']:
    flow_ids.append(extra.split("'flowid': '")[1].split("'")[0])
flow_ids = list(set(flow_ids))
for flow_id in flow_ids:
    events_i, total_calls_fi = analyze_logs(df, flow_id)
    events = pd.concat((events, events_i), ignore_index=True)
    total_calls[flow_id] = dict(total_calls_fi)

total_calls = pd.DataFrame.from_dict(total_calls, orient='index').fillna(0).astype(int)
#%% md
# Showing the results
#%%
print("Calculating stats...")

all_event_types_df = pd.DataFrame(all_event_types, columns=["Function", "Event Type"])

dfc = events.groupby(["Function", "Event Type"]).size().reset_index(name='Count')

merged_df = all_event_types_df.merge(dfc, on=["Function", "Event Type"], how="left")

# Fill missing counts with 0
merged_df["Count"] = merged_df["Count"].fillna(0).astype(int)

merged_df
#%%
problematic_events = events[(events["Event Type"] != "Log message") & (events["Event Type"] != "Final Answer")]


def breakup_log_message_data(row):
    row = copy(row)

    if row["Log message data"] is None:
        return pd.Series({**row})

    return pd.Series({**row, **row["Log message data"]})


problematic_events = problematic_events.apply(breakup_log_message_data, axis=1)
problematic_events.drop(columns=["Log message data"], inplace=True)
problematic_events = problematic_events[
    ["Event Type", "flow_id", "message", "extra", "function", "level", "line", "name", "time"]]

display(problematic_events.head(10))


#%%
problematic_events["Event Type"].value_counts()
#%%
problematic_events[problematic_events["Event Type"] == "Error"]
#%% md
prvni dva pripady - ve vystupu NPF to i na druhy pokus nemluvilo o jedne ze 3 rakert, vyhozena z carouselu

dalsi 3 pipady jsou jedna konv, kde v NPF se to dlouho zdrzelo, to naslo 3 rakety, ale odpoved byla timelimit, tak se vypraznil carousel

tedy zafungovaly opravne mechanismy
#%%
problematic_events[problematic_events["Event Type"] == "Invalid Format: Missing Final Answer"]
#%% md
ta vyjimka zpusobi dalsi obratku a agent preformuluje odpoved zda se uspesne s Final Answer
#%%
problematic_events[problematic_events["Event Type"] == "Timeout"]
#%% md
vzniklo to na volani embedingu v NPF
#%%
problematic_events[problematic_events["Event Type"] == "Warning"]
#%%
problematic_events[(problematic_events["Event Type"] == "Warning") & problematic_events["message"].str.contains("without using")]
#%% md
asi 3x FP pridano do sady
#%%
problematic_events[(problematic_events["Event Type"] == "Warning") & problematic_events["message"].str.contains("am working")]
#%% md
5 zachytu dobrych, zadne FP, jeden pripad nezachycen
#%%
problematic_events[(problematic_events["Event Type"] == "Warning") & ~problematic_events["message"].str.contains("am working") & ~problematic_events["message"].str.contains("without using")]