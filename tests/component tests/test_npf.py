import copy
import os.path
import pprint

import dotenv
import loguru
import pandas as pd
from loguru import logger
from tqdm import tqdm
from pathlib import Path

from sportega_rockets.config_manager import ConfigManager
from sportega_rockets.tools.query_responser_pack.npf import NearestProductsFinder
from sportega_rockets.tools.query_responser_pack.npf import get_examples

dotenv.load_dotenv()

PRODUCT_NAME = "tenis > vložky do bot"

script_dir = Path(__file__).resolve().parent
catalog_config_abs = script_dir / ".." / ".." / "data" / "config" / "catalog-tables.yml"
CONNECTION_STRING = os.environ.get("CONNECTION_STRING")
config_manager = ConfigManager()
config_manager.configure(
    connection_string=CONNECTION_STRING,
    catalog_config_path=str(catalog_config_abs),
)
catalogs = config_manager.get_catalogs()
catalog_descriptions = catalogs[PRODUCT_NAME].descriptions
catalog_products = catalogs[PRODUCT_NAME].products
catalog_embeddings = catalogs[PRODUCT_NAME].embeddings

domain = config_manager.config["domain_settings"][PRODUCT_NAME]

npf_excluded_columns = ["link", "image_link", "link_master", "image_link_master", "description", "description_embed", "rank"]
df_npf = catalog_products.drop(columns=[col for col in npf_excluded_columns if col in catalog_products.columns])

tool = NearestProductsFinder(
    agent=None,
    fetch_columns=domain["fetch_columns"].split(",") + ["id", "item_group_id"],  # pyright:ignore
    logger=logger.bind(domain=PRODUCT_NAME),
    catalog=df_npf,
    catalog_embeddings=catalog_embeddings,
    domain_name=domain,
    npf_configuration=domain["npf_config"],
)


def generate_examples(df):
    results = []

    for index, row in df.iterrows():
        #  time.sleep(20)
        if PRODUCT_NAME == "tenis > rakety":
            query = row["queries"].replace("product_type", "player_type")
            query = query.replace("velikost_hlavy", "velikost_hlavy_cm2")
        else:
            query = row["queries"]

        examples = {}

        for i in range(10):
            try:
                examples[i] = get_examples(
                    query=query,
                    df=tool.products,
                    cols_float=tool.float_cols,
                    logger=loguru.logger,
                    cat_enums=tool.cats_enums,
                    embed_cols=tool.text_cols,
                    llm=tool.llm,
                    config=tool.config,
                )
            except Exception as e:
                logger.error(f"{e}")
                row["Exception"] = str(e)
                # raise

        row["queries"] = query
        row["examples"] = pprint.pformat(examples)
        results.append(row)
        logger.info(f"Processed row {index}")

    results_df = pd.DataFrame(results, columns=[*df.columns, "examples"])
    return results_df


def fancy_repr(df, relevant_cols):
    exclude_columns_pres = ["row_id", "distance", "distance_debug"]
    pres_columns = [col for col in relevant_cols if col not in exclude_columns_pres]

    results = ""

    for i, row in df.iterrows():
        results += f". == {row['title']} == .\n"
        results += f"id={row['id']}\n"

        for col in pres_columns:
            if col == "title" or col == "id":
                continue

            results += f"{col}={row[col]}\n"

        results += "\n"

    return results


def tool_pertrubed(row):
    if PRODUCT_NAME == "tenis > rakety":
        query = row["queries"].replace("product_type", "player_type")
    else:
        query = row["queries"]

    exceptions = 0
    null_results = 0
    skipped = 0

    relevant_cols = None

    results = []
    exceptions_str = []

    all_examples = []

    used_examples = set()

    for i in range(10):

        # query += i * " "  # give the model something to think about

        try:

            if "examples" in row:
                # print("Using cached examples...")
                examples = eval(row["examples"])
                examples_org = examples
                examples = examples[i] if i in examples else examples.get(next(iter(examples), None)) if examples else None

                if i not in examples_org:
                    print(f"Used examples[0]... (bug?) ({i}, keys: {examples_org.keys()})")

            else:
                examples = None

            if str(examples) not in used_examples:
                print(f"Running... {examples}")
                df_results, relevant_cols, _ = tool._response(query, examples=copy.deepcopy(examples))
            else:
                print("Skipping...")
                skipped += 1
                continue

            used_examples.add(str(examples))

            all_examples += examples

            if len(examples) == 0:
                null_results += 1
                continue

            results.append((df_results[0:3].assign(points=[3, 2, 1])))
        except Exception as e:
            raise
            # exceptions += 1
            # exceptions_str += (type(e), str(e))
            # continue

    points = pd.concat(results, axis="index", ignore_index=True)
    points = points[["row_id", "points"]].groupby("row_id").sum()

    df = pd.merge(points, tool.products, how="left", on="row_id")

    return pd.Series(
        {
            **row,
            "results": fancy_repr(df, relevant_cols + ["points"]),
            "error_count": exceptions,
            "empty_count": null_results,
            "skipped": skipped,
            "exceptions_str": pprint.pformat(exceptions_str),
        }
    )


def main():

    from argparse import ArgumentParser

    parser = ArgumentParser()
    parser.add_argument("--examples", action="store_true")
    parser.add_argument("--npf", action="store_true")
    parser.add_argument("--full", action="store_true")
    parser.add_argument("--single", action="store_true")
    parser.add_argument("--atonce", action="store_true")

    args = parser.parse_args()

    examples = args.examples or args.full or not os.path.isfile("npf_w_examples.csv")
    npf = args.npf or args.full

    pd.set_option("display.max_columns", None)
    pd.set_option("display.width", None)
    tqdm.pandas()

    print("Starting...")

    if args.single:
        queries = [
            "df[df[‘Velikost gripu’] == “L7”][“id”].tolist()",
            "df[(df['Kategorie délky rakety'].str.contains('prodloužená délka', case=False, na=False)) & (df['Vzor výpletu'] == '16 x 16')]['id'].tolist()",
            "df[(df['player_type'].str.contains('pokročilé hráče', case=False, na=False)) & (df['Vzor výpletu'] == '16 x 16') & (df['vyvazeni'] == 340) & (df['velikost_hlavy_cm2_cm2'] == 594)]['id'].tolist()",
        ]

        for q in queries:
            get_examples(
                query=q,
                client=tool.client,
                df=tool.products,
                cols_float=tool.float_cols,
                logger=loguru.logger,
                cat_enums=tool.cats_enums,
                embed_cols=tool.text_cols,
                chat_model=tool.chat_model,
            )

    script_dir = Path(__file__).resolve().parent

    if examples:
        print("Generating examples...")
        df = pd.read_csv(script_dir / "npf_questions.csv")
        result_df = generate_examples(df)
        result_df.to_csv(script_dir / "npf_w_examples.csv", index=False)

    if npf:
        df = pd.read_csv(script_dir / "npf_w_examples.csv")
        df = df.progress_apply(tool_pertrubed, axis=1)
        df.to_csv(script_dir / "npf_stats.csv", index=False)

    if args.atonce:
        df = pd.read_csv(script_dir / "npf_questions.csv")
        _ = df["queries"].map(lambda x: tool.response(x))


if __name__ == "__main__":
    main()
