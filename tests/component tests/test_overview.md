# Component Tests Overview

This document provides an overview of the test scripts in the component tests folder. The scripts are categorized into
generator scripts and tester scripts.

## Generator Scripts

### npf_generator.py

The Nearest Product Finder (NPF) generator script is used to generate test questions for the NearestProductFinder tool.
It performs the following tasks:

1. **Question Generation**:
    - Generates a specified number of questions about non-existing products

2. **Query Generation**:
    - For each generated question, creates a dataframe query using the DataResponser tool

The script is essential for creating test data that can be used to evaluate the performance of the NearestProductFinder

### question_generator.py

The Question Generator script is a versatile tool for generating different types of questions about products. It
supports five main question types:

1. **Question Types**:
    - `filter`: Questions that specify product parameters and customer preferences
    - `general`: Questions that typically require numerical or parameter-based responses
    - `knowledge`: Open-ended questions about sports equipment
    - `title`: Product title generation with and without category names
    - `description`: Questions based on real product descriptions


2. **Usage**:
    - Use filter, general and title to generate training data for input classifier
    - Use filter and description to generate questions for rag_tester.py
    - Use filter, general and description to generate questions for complex_tester.py

## Tester Scripts

### test_npf.py

The Nearest Product Finder (NPF) tester script is used to evaluate the performance of the NearestProductsFinder tool. It
performs the following tasks:

1. **Test Execution**:
    - For each query, generates multiple example sets (default: 10)
    - Run the npf tool for each query
    - Return recommended products with IDs and relevant columns

The script is essential for validating the NearestProductsFinder tool's ability to find relevant products based on user
queries and for identifying potential issues in the recommendation system.

### rag_tester.py

The RAG Tester script evaluates the performance of the DescriptionFinder tool in retrieving relevant product
descriptions. It performs the following tasks:

1. **Test Execution**:
    - Processes each query using the DescriptionFinder tool
    - Retrieves top 5 product matches for each query
    - Uses GPT-4.1 to evaluate the relevance of matched descriptions

2. **Evaluation Process**:
    - For each matched product, compares the question with the product description
    - Uses LLM to determine if the description fully matches the query requirements

The script is essential for validating the RAG system's ability to retrieve relevant product descriptions and ensuring
that the retrieved descriptions actually match user queries.

### complex_tester.py

The Complex Tester script evaluates the complete shopping assistant agent API endpoint by simulating real user
interactions. It performs the following tasks:

1. **Test Setup**:
    - Configures API endpoint URL (default: http://localhost:8080/api/chat/answer)
    - Loads test cases from a CSV file containing user queries

2. **Test Execution**:
    - Sends each user query to the API endpoint

3. **Result Processing**:
    - Stores API responses including:
        - Output text
        - Product recommendations
        - Response times

The script is essential for end-to-end testing of the shopping assistant agent's API, ensuring it can handle real user
queries and provide appropriate responses within acceptable timeframes.
