[project]
name = "sportega-rockets"
version = "0.1.0"
description = ""
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]

dependencies = [
    "SQLAlchemy==2.0.30",
    "black==24.8.0",
    "cachetools>=5.5.0",
    "diskcache>=5.6.3",
    "fastapi>=0.115.4",
    "langchain-community>=0.3.5",
    "langchain-core>=0.3.15",
    "langchain-experimental>=0.3.3",
    "langchain-openai>=0.2.6",
    "langchain-text-splitters>=0.3.2",
    "langchain>=0.3.22",
    "langgraph>=0.3.25",
    "loguru==0.7.2",
    "numpy==1.26.4",
    "openai>=1.54.3",
    "openpyxl>=3.1.5",
    "pandas==2.2.2",
    "psycopg2-binary>=2.9.10",
    "pydantic~=2.0",
    "python-dotenv==1.0.1",
    "pytz==2024.1",
    "scikit-learn==1.5.1",
    "scipy==1.13.0",
    "tabulate>=0.9.0",
    "tiktoken==0.7.0",
    "uvicorn>=0.34.0",
    "matplotlib>=3.10.0",
    "pyyaml>=6.0.2",
    "papermill>=2.6.0",
    "langchain-google-genai>=2.1.2",
    "langgraph>=0.3.25",
    "pystemmer>=3.0.0",
    "bm25s>=0.2.12",
    "pgvector>=0.4.1",
]
readme = "README.md"
requires-python = ">= 3.11"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = [
    "hatchling>=1.25.0",
    "odfpy>=1.4.1",
    "jupyterlab>=4.3.0",
    "google-auth>=2.36.0",
    "google-auth-oauthlib>=1.2.1",
    "google-auth-httplib2==0.2.*",
    "google-api-python-client>=2.153.0",
    "gsheet-pandas==0.2.*",
    "googledriver>=0.1.7",
    "openpyxl>=3.1.5",
    "py-spy>=0.4.0",
    "graphviz>=0.20.3",
]

[tool.black]
line-length = 139
target-version = ['py311']

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/sportega_rockets"]
