#%%
import numpy as np
import dotenv
import os

dotenv.load_dotenv()

CONNECTION_STRING="postgresql://dev:<EMAIL>/sportega"
OPENAI_KEY=os.environ["OPENAI_API_KEY"]
#%%
import pandas as pd
df = pd.read_csv('catalog.csv',sep=';',dtype={'item_group_id': str})
df.columns
#%%
df.shape[0]
#%% md
## hmotnost
#%%
df['hmotnost'] = df['Hmotnost tenisové rakety'].str[:3].astype(float)
df[['Hmotnost tenisové rakety','hmotnost']].head()
#%% md
## hmotnostni trida
#%%
df['Hmotnostní třída'] = df['Hmotnostní třída'].str.replace('(','').str.replace(')','')
#%% md
## modelova rada
#%%
df['Modelová řada'] = df['brand']+ ' ' + df['Modelová řada']
#%% md
## vyvazeni
#%%
df['Vyvážení rakety'] = np.where(df['Vyvážení rakety'] == 'neudává se', None, df['Vyvážení rakety'])
#%%
df['vyvazeni'] = df['Vyvážení tenisové rakety'].fillna(df['Vyvážení rakety']).combine_first(df['Vyvážení rakety']).str[:3].astype(float) # float kvuli NaN
df[['vyvazeni','Vyvážení tenisové rakety','Vyvážení rakety']]
#%% md
## velikost hlavy
#%%
df['velikost_hlavy_cm2'] = df['Velikost hlavy'].str[:3].astype(int)
df[['Velikost hlavy','velikost_hlavy_cm2']].head()
#%%
df['velikost_hlavy_in2'] = (df['velikost_hlavy_cm2'] * 0.155).round().astype(int)
#%%
df['velikost_hlavy_in2'].head() 
#%% md
## delka rakety
#%%
df['delka'] = df['Délka tenisové rakety'].fillna(df['Délka rakety']).combine_first(df['Délka rakety']).str[:3].astype(float) # float kvuli None
df[['delka','Délka tenisové rakety','Délka rakety']]
#%%
df.delka.unique()
#%% md
## kategorie delky
#%%
df['Kategorie délky rakety'] = df['Kategorie délky rakety'].str.replace('(','').str.replace(')','')
#%% md
## material
#%%
df['material'] = df['Materiál rámu rakety'].fillna(df['Materiál']).combine_first(df['Materiál'])
df[['material','Materiál rámu rakety','Materiál']]
#%% md
## vek
#%%
df['Věk'] = df['Věk'].str.replace('–', '-')

#%%
df['age_min'] = df['Věk'].str.split('-').str[0]
df['age_max'] = df['Věk'].str.split('-').str[1]
df[df.age_min.notna()][['age_min', 'age_max', 'Věk']]
#%%
df['age_min'] = df['age_min'].str.extract(r'(\d+)')
df['age_max'] = df['age_max'].str.extract(r'(\d+)')
df[df.age_min.notna()][['age_min', 'age_max', 'Věk']]
#%%
df[(df['age_min'].isna() | df["age_max"].isna())&df["Věk"].notna()][['age_min', 'age_max', 'Věk']]
#%%
# df['age_max'] = np.where(df['age_max'].notna(), df['age_max'], 12) # od 13 vys se hleadaji velmi lehke mezi dospelymi...
#%% md
## vyska
#%%
df['Výška hráče'] = df['Výška hráče'].str.replace('–', '-')
df['vyska_hrace_min'] = df['Výška hráče'].str.split('-').str[0]
df['vyska_hrace_max'] = df['Výška hráče'].str.split('-').str[1]
df[df.age_min.notna()][['vyska_hrace_min', 'vyska_hrace_max', 'Výška hráče']]

#%%
df.vyska_hrace_min.value_counts()
#%%
df.vyska_hrace_max = np.where(df.vyska_hrace_min.str.contains('do'), df.vyska_hrace_min, df.vyska_hrace_max)
df.vyska_hrace_min = np.where(df.vyska_hrace_min.str.contains('do'), np.nan, df.vyska_hrace_min)
#%%
df['vyska_hrace_min'] = df['vyska_hrace_min'].str.extract(r'(\d+)')
df['vyska_hrace_max'] = df['vyska_hrace_max'].str.extract(r'(\d+)')
df[df.vyska_hrace_min.notna()][['vyska_hrace_min', 'vyska_hrace_max', 'Výška hráče']]
#%%
df[df['Výška hráče'].str.contains('do',na=False)][['vyska_hrace_min', 'vyska_hrace_max', 'Výška hráče']]
#%%
df[df['Výška hráče'].notna() & df["vyska_hrace_max"].isna()][['vyska_hrace_min', 'vyska_hrace_max', 'Výška hráče']]
#%% md
### cena
#%%
df['price_czk'] = df['price'].str.extract(r'(\d+)')
#%% md
### barva
#%%
df['color'] = df['color'].str.replace(',','/')
df.rename(columns={"color": "barva"}, inplace=True) # funguje lip pro ceske nazvy barev

#%%
df.barva.value_counts()
#%%
df.columns
#%% md
## tuhost ramu
#%%
df.rename(columns={'Tuhost rámu:':'Tuhost rámu'}, inplace=True)
#%%
df['Tuhost rámu'] = df['Tuhost rámu'].str.replace(' RA', '')
df['Tuhost rámu'].unique()
#%% md
# masterprodukty
#%%
df_m = pd.read_csv('masterprodukty_dospele.csv',sep=';',dtype={'id':str})
df_d = pd.read_csv('masterprodukty_detske.csv',sep=';',dtype={'id':str})
df_m.drop_duplicates(keep='first',inplace=True)
df_d.drop_duplicates(keep='first',inplace=True)
#%%
df_m.columns
#%%
df_m = df_m[['id','link','g:image_link','title']].rename(columns={'link':'link_master','g:image_link': 'image_link_master', 'id':'item_group_id', 'title':'group_title'})
df_d = df_d[['id','link','g:image_link','title']].rename(columns={'link':'link_master','g:image_link': 'image_link_master', 'id':'item_group_id', 'title':'group_title'})
#%%
df_c = pd.read_csv('chybejici_masters_240920.csv',sep=';',dtype={'id':str})
df_c = df_c[['id','g:image_link','link','title']].rename(columns={'id':'item_group_id','g:image_link':'image_link_master','link':'link_master','title':'group_title'})
#%%
df_m = pd.concat([df_m, df_d, df_c], ignore_index=True)
#%%
df  = df.merge(df_m, on='item_group_id',how='left')
#%%
df.columns
#%% md
## title a group_title
#%%
df['title'] = df['title'].str.replace('Tenisová raketa ','').str.replace('Dětská tenisová raketa ','')
df['group_title'] = df['group_title'].fillna(df['title'])  # kvuli ProductPickeru
df['group_title'] = df['group_title'].str.replace('Tenisová raketa ','').str.replace('Dětská tenisová raketa ','')

#%%
pd.set_option('display.max_colwidth', None)
#%%
df[df['title'].str.contains('       ')]['title']
#%%
df['title'] = df['title'].str.replace(r'\s{2,}', ' ', regex=True)
df['group_title'] = df['group_title'].str.replace(r'\s{2,}', ' ', regex=True)
#%% md
## master products from variants without masterproduct 
#%%
df.loc[df['image_link_master'].isnull(), 'image_link_master'] = df['image_link']
df.loc[df['link_master'].isnull(), 'link_master'] = df['link']
df.loc[df['item_group_id'].isnull(), 'item_group_id'] = 'child_' + df['id']
#%% md
## product type
#%%
df.rename(columns={'product_type':'player_type'}, inplace=True)
#%%
df.player_type.value_counts()   
#%% md
## vyber sloupcu do katalogu
#%%
cols = ['group_title','title','availability', 'brand', 'barva', 'condition', 'image_link', 'image_link_master',
    'item_group_id', 'material', 'price_czk', 'player_type', 'id', 'link', 'link_master',
    'Velikost gripu', 'Vypletená raketa', 'Kategorie délky rakety', 
    'Vzor výpletu', 'Určeno pro kategorii hráčů',
    'Zaměření', 'Roční kolekce', 'Modelová řada', 'Použitá základní omotávka', 'Používá', 
    'Švihová hmotnost (s výpletem)','Tuhost rámu',
    'Technologie', 'Obal rakety',
    'hmotnost', 'Hmotnostní třída',
    'vyvazeni', 'velikost_hlavy_cm2', 'velikost_hlavy_in2','delka', 'age_min', 'age_max',
    'vyska_hrace_min', 'vyska_hrace_max']
#%%
df = df[cols]
#%%
df.columns
#%% md
## odstraneni kategorie zeny
#%%
df = df[~df.player_type.str.contains('ženy',na=False)]
#%% md
## split vzor výpletu na Vzor výpletu X a Vzor výpletu Y
#%%
df["Vzor výpletu X"] = df["Vzor výpletu"].str.split("x").map(lambda x: float(x[0]), na_action='ignore')
df["Vzor výpletu Y"] = df["Vzor výpletu"].str.split("x").map(lambda x: float(x[1]), na_action='ignore')
#%% md
## Odmazat veci v zavorce v gripu
#%%
df['Velikost gripu'] = df['Velikost gripu'].str.replace(r'\(.*?\)', '', regex=True).str.strip()
#%% md
## ulozeni
#%%
# df.to_csv('katalog9.csv', columns=cols, index=False,sep=';')
#%%
# not str types

from sqlalchemy import Integer, Float

types = {
    'price_czk': Integer,
    'Švihová hmotnost (s výpletem)': Float,
    'Tuhost rámu': Float,
    'hmotnost': Integer,
    'vyvazeni': Integer,
    'velikost_hlavy_cm2': Integer,
    'velikost_hlavy_in2': Integer,
    'delka': Integer,
    'age_min': Integer,
    'age_max': Integer,
    'vyska_hrace_min': Integer,
    'vyska_hrace_max': Integer,
    "Vzor výpletu X": Integer,
    "Vzor výpletu Y": Integer,
}
#%%
df.to_sql('products', con=CONNECTION_STRING, if_exists='replace', index=False,dtype=types)
#%%
import os
import numpy as np
import openai
import pandas as pd
from sqlalchemy import types

def extract_bytes(e: openai.Embedding) -> bytes:
    return np.array(e.embedding).astype(np.float64).tobytes()

# We need this to refresh the datatypes
df = pd.read_sql("products", CONNECTION_STRING)

cols = df.select_dtypes(exclude=[np.number]).columns.tolist()
cols = [x for x in cols if "link" not in x and "id" not in x]

strings = set()

for col in cols:
    strings = strings | set(df[col].unique())

dfe = pd.read_sql("products_embeddings_binary", CONNECTION_STRING)
dfe = dfe.set_index("word")

print(f"{len(strings)=}")

new_strings = [x for x in strings if x not in dfe.index]
new_strings = [x for x in new_strings if isinstance(x, str)]

print(f"{len(new_strings)=}")

if len(new_strings) > 0:
    client = openai.Client(api_key=os.environ["OPENAI_API_KEY"])

    embeds = []

    for i in range(0, len(new_strings), 2000):
        new_strings_batch = new_strings[i:i+2000]
        embeds_batch = client.embeddings.create(
            input=new_strings_batch,
            model="text-embedding-3-large",
        )
        embeds += embeds_batch.data

    embeds_b = list(map(extract_bytes, embeds))

    df_new = pd.DataFrame({"word": new_strings, "binary": embeds_b}).set_index(["word"])

    df_updated = pd.concat([dfe, df_new], axis=0)

    print(f"{len(dfe)=}")
    print(f"{len(df_new)=}")
    print(f"{len(df_updated)=}")

    df_updated.to_sql("products_embeddings_binary", CONNECTION_STRING, if_exists="replace", dtype={"binary": types.LargeBinary})
#%%
strings = list(sorted(map(str, strings)))
#%% md
# kontroly
#%%
df = pd.read_sql('products', con='postgresql://dev:<EMAIL>:5432/sportega')
#%%
df[df.title.str.contains(',')]['title']
#%%
df[df['title'].str.contains('  ')]['title']
#%%
df.shape[0]
#%%
df[~df.item_group_id.isin(df_m.item_group_id) & ~df.item_group_id.str.startswith('child')][['id','item_group_id','link','link_master','image_link','image_link_master','availability']]
#%%
df[df.group_title.isna()]
#%%
df[df.link_master.isna()]
#%%
df[df.image_link_master.isna()]
#%%
df[df.group_title.isna()]
#%%
df[("child_"+df.id == df.item_group_id) & ((df.link != df.link_master) | (df.image_link != df.image_link_master))][['id','item_group_id','link','link_master','image_link','image_link_master']]
#%%
df[("child_"+df.id != df.item_group_id) & ((df.link == df.link_master) | (df.image_link == df.image_link_master))][['id','item_group_id','link','link_master','image_link','image_link_master']]