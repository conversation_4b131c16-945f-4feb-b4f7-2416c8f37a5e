# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.4
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
ansicolors==1.1.8
    # via papermill
anyio==4.9.0
    # via httpx
    # via openai
    # via starlette
attrs==25.3.0
    # via aiohttp
    # via jsonschema
    # via referencing
black==24.8.0
    # via sportega-rockets
bm25s==0.2.13
    # via sportega-rockets
cachetools==5.5.2
    # via google-auth
    # via sportega-rockets
certifi==2025.4.26
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via black
    # via papermill
    # via uvicorn
contourpy==1.3.2
    # via matplotlib
cycler==0.12.1
    # via matplotlib
dataclasses-json==0.6.7
    # via langchain-community
diskcache==5.6.3
    # via sportega-rockets
distro==1.9.0
    # via openai
entrypoints==0.4
    # via papermill
et-xmlfile==2.0.0
    # via openpyxl
fastapi==0.115.12
    # via sportega-rockets
fastjsonschema==2.21.1
    # via nbformat
filetype==1.2.0
    # via langchain-google-genai
fonttools==4.58.1
    # via matplotlib
frozenlist==1.6.0
    # via aiohttp
    # via aiosignal
google-ai-generativelanguage==0.6.18
    # via langchain-google-genai
google-api-core==2.24.2
    # via google-ai-generativelanguage
google-auth==2.40.2
    # via google-ai-generativelanguage
    # via google-api-core
googleapis-common-protos==1.70.0
    # via google-api-core
    # via grpcio-status
greenlet==3.2.2
    # via sqlalchemy
grpcio==1.71.0
    # via google-api-core
    # via grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via httpcore
    # via uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via langgraph-sdk
    # via langsmith
    # via openai
httpx-sse==0.4.0
    # via langchain-community
idna==3.10
    # via anyio
    # via httpx
    # via requests
    # via yarl
jiter==0.10.0
    # via openai
joblib==1.5.1
    # via scikit-learn
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.24.0
    # via nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter-client==8.6.3
    # via nbclient
jupyter-core==5.8.1
    # via jupyter-client
    # via nbclient
    # via nbformat
kiwisolver==1.4.8
    # via matplotlib
langchain==0.3.25
    # via langchain-community
    # via sportega-rockets
langchain-community==0.3.24
    # via langchain-experimental
    # via sportega-rockets
langchain-core==0.3.62
    # via langchain
    # via langchain-community
    # via langchain-experimental
    # via langchain-google-genai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via langgraph-prebuilt
    # via sportega-rockets
langchain-experimental==0.3.4
    # via sportega-rockets
langchain-google-genai==2.1.5
    # via sportega-rockets
langchain-openai==0.3.18
    # via sportega-rockets
langchain-text-splitters==0.3.8
    # via langchain
    # via sportega-rockets
langgraph==0.4.7
    # via sportega-rockets
langgraph-checkpoint==2.0.26
    # via langgraph
    # via langgraph-prebuilt
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.43
    # via langchain
    # via langchain-community
    # via langchain-core
loguru==0.7.2
    # via sportega-rockets
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.3
    # via sportega-rockets
multidict==6.4.4
    # via aiohttp
    # via yarl
mypy-extensions==1.1.0
    # via black
    # via typing-inspect
nbclient==0.10.2
    # via papermill
nbformat==5.10.4
    # via nbclient
    # via papermill
numpy==1.26.4
    # via bm25s
    # via contourpy
    # via langchain-community
    # via matplotlib
    # via pandas
    # via pgvector
    # via scikit-learn
    # via scipy
    # via sportega-rockets
openai==1.82.0
    # via langchain-openai
    # via sportega-rockets
openpyxl==3.1.5
    # via sportega-rockets
orjson==3.10.18
    # via langgraph-sdk
    # via langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==24.2
    # via black
    # via langchain-core
    # via langsmith
    # via marshmallow
    # via matplotlib
pandas==2.2.2
    # via sportega-rockets
papermill==2.6.0
    # via sportega-rockets
pathspec==0.12.1
    # via black
pgvector==0.4.1
    # via sportega-rockets
pillow==11.2.1
    # via matplotlib
platformdirs==4.3.8
    # via black
    # via jupyter-core
propcache==0.3.1
    # via aiohttp
    # via yarl
proto-plus==1.26.1
    # via google-ai-generativelanguage
    # via google-api-core
protobuf==5.29.5
    # via google-ai-generativelanguage
    # via google-api-core
    # via googleapis-common-protos
    # via grpcio-status
    # via proto-plus
psycopg2-binary==2.9.10
    # via sportega-rockets
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.5
    # via fastapi
    # via langchain
    # via langchain-core
    # via langchain-google-genai
    # via langgraph
    # via langsmith
    # via openai
    # via pydantic-settings
    # via sportega-rockets
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via langchain-community
pyparsing==3.2.3
    # via matplotlib
pystemmer==3.0.0
    # via sportega-rockets
python-dateutil==2.9.0.post0
    # via jupyter-client
    # via matplotlib
    # via pandas
python-dotenv==1.0.1
    # via pydantic-settings
    # via sportega-rockets
pytz==2024.1
    # via pandas
    # via sportega-rockets
pyyaml==6.0.2
    # via langchain
    # via langchain-community
    # via langchain-core
    # via papermill
    # via sportega-rockets
pyzmq==26.4.0
    # via jupyter-client
referencing==0.36.2
    # via jsonschema
    # via jsonschema-specifications
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via google-api-core
    # via langchain
    # via langchain-community
    # via langsmith
    # via papermill
    # via requests-toolbelt
    # via tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rpds-py==0.25.1
    # via jsonschema
    # via referencing
rsa==4.9.1
    # via google-auth
scikit-learn==1.5.1
    # via sportega-rockets
scipy==1.13.0
    # via bm25s
    # via scikit-learn
    # via sportega-rockets
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
    # via openai
sqlalchemy==2.0.30
    # via langchain
    # via langchain-community
    # via sportega-rockets
starlette==0.46.2
    # via fastapi
tabulate==0.9.0
    # via sportega-rockets
tenacity==9.1.2
    # via langchain-community
    # via langchain-core
    # via papermill
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.7.0
    # via langchain-openai
    # via sportega-rockets
tornado==6.5.1
    # via jupyter-client
tqdm==4.67.1
    # via openai
    # via papermill
traitlets==5.14.3
    # via jupyter-client
    # via jupyter-core
    # via nbclient
    # via nbformat
typing-extensions==4.13.2
    # via anyio
    # via fastapi
    # via langchain-core
    # via openai
    # via pydantic
    # via pydantic-core
    # via referencing
    # via sqlalchemy
    # via typing-inspect
    # via typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic
    # via pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via sportega-rockets
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zstandard==0.23.0
    # via langsmith
