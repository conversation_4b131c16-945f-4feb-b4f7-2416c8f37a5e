#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
# from langchain.tools import BaseTool
# from typing import Any, Literal, Union
# from pydantic import BaseModel, Field
#%%
df = pd.read_csv('categories_from_web_manual_flattened_with_other.csv',sep=';')
df
#%%
from pydantic import BaseModel
from typing import Literal

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist()

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: Literal[tuple(categories)]
#%%
o = Output(category='tenis > míčky')
print(o)
#%%
OPENAI_API_KEY='*******************************************************************************************************************************************'
llm = ChatOpenAI(
            openai_api_key=OPENAI_API_KEY,
            temperature=0,
            model="gpt-4o",
        )
structured_llm = llm.with_structured_output(schema=Output, method="function_calling")

prompt = f"""\
Decide which category from the following categories the text belongs to.
The categories are: {categories}
The text is: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
question="jaka je nejdrazsi tenisova raketa?"
chain.invoke({"text": question}).category
#%%
question="jaka je dnes pocasi?"
chain.invoke({"text": question}).category