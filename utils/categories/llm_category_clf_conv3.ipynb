#%% md
# pokusy s promptem pro prodavate rakety - zacatecnik
#%%
import pandas as pd
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

#%%
df = pd.read_csv('categories_from_web_manual_flattened.csv',sep=';')

#%%
from pydantic import BaseModel
from typing import Literal, List

# Extract unique values from the 'category' column
categories = df['category'].unique().tolist() + ['other']

# Dynamically create a Pydantic model
class Output(BaseModel):
    category: List[Literal[tuple(categories)]]
#%%
OPENAI_API_KEY='*******************************************************************************************************************************************'
llm = ChatOpenAI(
            openai_api_key=OPENAI_API_KEY,
            temperature=0,
            model="gpt-4o-2024-08-06",
            streaming=False,
        )
structured_llm = llm.with_structured_output(schema=Output, method="function_calling", strict=True)

prompt = f"""\
Decide which categories from the following categories the last user message from conversation belongs to. \
If the text belongs to none of the categories, return 'other'. \
If the can belong to multiple categories, return list of all possible categories.
The categories are: {categories}
The conversation: {{text}}
"""

prompt_template = PromptTemplate.from_template(prompt)
chain = prompt_template | structured_llm

#%%
messages = "HumanMessage(content='prodavate rakety?'),AIMessage(content='Ano, prodáváme tenisové rakety. Jak vám mohu pomoci s výběrem? Jste začátečník, pokročilý hráč, nebo hledáte raketu pro dítě?'),HumanMessage(content='zacatecnik')"
#%%
chain.invoke({"text": messages})