#%%
import pandas as pd

#%%
import pandas as pd

def cols_cmp(category):
    # Vytvoření přehledu sloupců s alespoň jednou nenulovou hodnotou pro každou podkategorii
    columns_by_category = {}
    category_by_product_type = category.groupby('product_type')
    for cat, df_cat in category_by_product_type:
        non_null_cols = df_cat.columns[df_cat.notnull().any()].tolist()
        columns_by_category[cat] = set(non_null_cols)

    # Výpis srovnání sloupců mezi kategoriemi

    all_columns = sorted(set.union(*columns_by_category.values()))
    comparison_df = pd.DataFrame(
        {cat: [col in cols for col in all_columns] for cat, cols in columns_by_category.items()},
        index=all_columns
    )
    comparison_df = comparison_df.astype(int)  # 1 = sloupec je v kategorii, 0 = není

    return comparison_df
#%%
from collections import defaultdict

# col_cal_map obsahuje DataFrame, kde index jsou názvy sloupců a sloupce jsou názvy kategorií, hodnoty 1/0
# Potřebujeme najít skupiny kategorií, které mají stejnou množinu sloupců (tj. stejný vektor 1/0 přes všechny sloupce)

def group_categories_by_columns(col_cal_map):
    # Transponujeme, aby řádky byly kategorie a sloupce sloupce
    tdf = col_cal_map.T
    # Pro každou kategorii vytvoříme tuple z vektoru 1/0 (přes všechny sloupce)
    col_signature = tdf.apply(lambda row: tuple(row.values), axis=1)
    # Skupiny: klíč je tuple (signature), hodnota je seznam kategorií
    groups = defaultdict(list)
    for cat, sig in col_signature.items():
        groups[sig].append(cat)
    # Výstup: seznam skupin (každá skupina je seznam kategorií se stejnou množinou sloupců)
    return list(groups.values())
#%%
def variant_columns(df):
    # Najdi sloupce, kde existuje alespoň jedna skupina item_group_id s více různými hodnotami
    
    inconsistent_cols = []
    group = df.groupby('item_group_id',dropna=True)
    for col in df.columns:
        if col == 'item_group_id':
            continue
        nunique_per_group = group[col].nunique(dropna=True)
        if (nunique_per_group > 1).any():
            inconsistent_cols.append(col)
    return inconsistent_cols
#%%
df_all = pd.read_csv('google_feed.csv',low_memory=False)
#%% md
# Ortezy
#%%
df=df_all[df_all['{http://base.google.com/ns/1.0}product_type'].str.contains('ortézy', na=False,case=False)].copy()
#%%
df = df.dropna(axis=1, how='all')
df.columns
#%%
df.columns = [col.split('}', 1)[1] if '}' in col else col for col in df.columns]
df.columns
#%%
COLS = ['custom_label_0',
 'custom_label_1',
 'custom_label_3',
 'custom_label_4',
 'google_product_category',
'gtin',
'identifier_exists',
 'shipping',
 'doprava_zdarma',
 'is_bundle',
'delivery_date',
 'product_length',
 'product_width',
 'product_height',
 'shipping_length',
 'shipping_width',
 'shipping_height',
 'shipping_weight',
 'rozmery_produktu',
 'rozmery_baleni',
 'customs_number',
'ads_grouping',
 'additional_image_link',
'custom_label_2']

df.drop(columns=COLS, inplace=True)
#%%
df.drop(columns=['id', 'description', 'link', 'image_link', 'availability',
       'price', 'brand', 'condition', 'stock', 'rank'], inplace=True)
#%%
df.columns
#%%
df.shape[0]
#%%
df['Typ produktu'].value_counts()
#%%
df.product_type.value_counts()
#%%
variant_columns(df)
#%% md
## Vlozky
#%%
vlozky = df[df.product_type.str.contains('vložky', na=False,case=False)].copy()
vlozky = vlozky.dropna(axis=1, how='all')
vlozky.columns
#%%
variant_columns(vlozky)
#%%
vlozky['Typ produktu'].value_counts()
#%% md
toto ma navic oproti parametrum, co maj vlozku v tenis-boty, ale je to ok
#%% md
maj ale taky navic pohlavi, modelovou radu a strih
#%%
vlozky['Pohlaví'].value_counts()
#%%
vlozky['Modelová řada'].value_counts()
#%%
vlozky['Střih'].value_counts()
#%%
vlozky_all=df_all[df_all['{http://base.google.com/ns/1.0}product_type'].str.contains('vložky', na=False,case=False)].copy()
vlozky_all['{http://base.google.com/ns/1.0}product_type'].value_counts()
#%%
df_all[df_all['{http://base.google.com/ns/1.0}product_type'].str.contains('obuv', na=False,case=False)]['{http://base.google.com/ns/1.0}product_type'].value_counts()
#%%
df_all[df_all['{http://base.google.com/ns/1.0}product_type'].str.contains('boty', na=False,case=False)]['{http://base.google.com/ns/1.0}product_type'].value_counts()